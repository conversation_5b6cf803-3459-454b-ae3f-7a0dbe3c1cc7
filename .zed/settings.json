{"languages": {"Markdown": {"tab_size": 2, "formatter": "prettier"}, "TOML": {"formatter": "prettier", "format_on_save": "off"}, "YAML": {"tab_size": 2, "formatter": "prettier"}, "JSON": {"tab_size": 2, "preferred_line_length": 120, "formatter": "prettier"}, "JSONC": {"tab_size": 2, "preferred_line_length": 120, "formatter": "prettier"}, "JavaScript": {"tab_size": 2, "formatter": "prettier"}, "CSS": {"tab_size": 2, "formatter": "prettier"}, "Rust": {"tasks": {"variables": {"RUST_DEFAULT_PACKAGE_RUN": "zed"}}}}, "file_types": {"Dockerfile": ["Dockerfile*[!dockerignore]"], "JSONC": ["**/assets/**/*.json", "renovate.json"], "Git Ignore": ["dockerignore"]}, "hard_tabs": false, "formatter": "auto", "remove_trailing_whitespace_on_save": true, "ensure_final_newline_on_save": true, "file_scan_exclusions": ["crates/assistant_tools/src/edit_agent/evals/fixtures", "crates/eval/worktrees/", "crates/eval/repos/", "**/.git", "**/.svn", "**/.hg", "**/.jj", "**/CVS", "**/.DS_Store", "**/Thumbs.db", "**/.classpath", "**/.settings"]}