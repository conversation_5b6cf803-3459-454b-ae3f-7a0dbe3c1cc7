name: "Build docs"
description: "Build the docs"

runs:
  using: "composite"
  steps:
    - name: Setup mdBook
      uses: peaceiris/actions-mdbook@ee69d230fe19748b7abf22df32acaa93833fad08 # v2
      with:
        mdbook-version: "0.4.37"

    - name: Cache dependencies
      uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
      with:
        save-if: ${{ github.ref == 'refs/heads/main' }}
        # cache-provider: "buildjet"

    - name: Install Linux dependencies
      shell: bash -euxo pipefail {0}
      run: ./script/linux

    - name: Check for broken links (in MD)
      uses: lycheeverse/lychee-action@82202e5e9c2f4ef1a55a3d02563e1cb6041e5332 # v2.4.1
      with:
        args: --no-progress --exclude '^http' './docs/src/**/*'
        fail: true

    - name: Build book
      shell: bash -euxo pipefail {0}
      run: |
        mkdir -p target/deploy
        mdbook build ./docs --dest-dir=../target/deploy/docs/

    - name: Check for broken links (in HTML)
      uses: lycheeverse/lychee-action@82202e5e9c2f4ef1a55a3d02563e1cb6041e5332 # v2.4.1
      with:
        args: --no-progress --exclude '^http' 'target/deploy/docs/'
        fail: true
