name: "Run tests"
description: "Runs the tests"

runs:
  using: "composite"
  steps:
    - name: Install Rust
      shell: bash -euxo pipefail {0}
      run: |
        cargo install cargo-nextest --locked

    - name: Install Node
      uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
      with:
        node-version: "18"

    - name: Limit target directory size
      shell: bash -euxo pipefail {0}
      run: script/clear-target-dir-if-larger-than 100

    - name: Run tests
      shell: bash -euxo pipefail {0}
      run: cargo nextest run --workspace --no-fail-fast
