name: Publish Collab Server Image

on:
  push:
    tags:
      - collab-production
      - collab-staging

env:
  DOCKER_BUILDKIT: 1

jobs:
  style:
    name: Check formatting and Clippy lints
    if: github.repository_owner == 'zed-industries'
    runs-on:
      - self-hosted
      - macOS
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false
          fetch-depth: 0

      - name: Run style checks
        uses: ./.github/actions/check_style

      - name: Run clippy
        run: ./script/clippy

  tests:
    name: Run tests
    runs-on:
      - self-hosted
      - macOS
    needs: style
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false
          fetch-depth: 0

      - name: Install cargo nextest
        shell: bash -euxo pipefail {0}
        run: |
          cargo install cargo-nextest --locked

      - name: Limit target directory size
        shell: bash -euxo pipefail {0}
        run: script/clear-target-dir-if-larger-than 100

      - name: Run tests
        shell: bash -euxo pipefail {0}
        run: cargo nextest run --package collab --no-fail-fast

  publish:
    name: Publish collab server image
    needs:
      - style
      - tests
    runs-on:
      - namespace-profile-16x32-ubuntu-2204
    steps:
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Sign into DigitalOcean docker registry
        run: doctl registry login

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Build docker image
        run: |
          docker build -f Dockerfile-collab \
            --build-arg "GITHUB_SHA=$GITHUB_SHA" \
            --tag "registry.digitalocean.com/zed/collab:$GITHUB_SHA" \
            .

      - name: Publish docker image
        run: docker push "registry.digitalocean.com/zed/collab:${GITHUB_SHA}"

      - name: Prune Docker system
        run: docker system prune  --filter 'until=72h' -f

  deploy:
    name: Deploy new server image
    needs:
      - publish
    runs-on:
      - namespace-profile-16x32-ubuntu-2204

    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Sign into Kubernetes
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 ${{ secrets.CLUSTER_NAME }}

      - name: Start rollout
        run: |
          set -eu
          if [[ $GITHUB_REF_NAME = "collab-production" ]]; then
            export ZED_KUBE_NAMESPACE=production
            export ZED_COLLAB_LOAD_BALANCER_SIZE_UNIT=10
            export ZED_API_LOAD_BALANCER_SIZE_UNIT=2
          elif [[ $GITHUB_REF_NAME = "collab-staging" ]]; then
            export ZED_KUBE_NAMESPACE=staging
            export ZED_COLLAB_LOAD_BALANCER_SIZE_UNIT=1
            export ZED_API_LOAD_BALANCER_SIZE_UNIT=1
          else
            echo "cowardly refusing to deploy from an unknown branch"
            exit 1
          fi

          echo "Deploying collab:$GITHUB_SHA to $ZED_KUBE_NAMESPACE"

          source script/lib/deploy-helpers.sh
          export_vars_for_environment $ZED_KUBE_NAMESPACE

          ZED_DO_CERTIFICATE_ID="$(doctl compute certificate list --format ID --no-header)"
          export ZED_DO_CERTIFICATE_ID
          export ZED_IMAGE_ID="registry.digitalocean.com/zed/collab:${GITHUB_SHA}"

          export ZED_SERVICE_NAME=collab
          export ZED_LOAD_BALANCER_SIZE_UNIT=$ZED_COLLAB_LOAD_BALANCER_SIZE_UNIT
          export DATABASE_MAX_CONNECTIONS=850
          envsubst < crates/collab/k8s/collab.template.yml | kubectl apply -f -
          kubectl -n "$ZED_KUBE_NAMESPACE" rollout status deployment/$ZED_SERVICE_NAME --watch
          echo "deployed ${ZED_SERVICE_NAME} to ${ZED_KUBE_NAMESPACE}"

          export ZED_SERVICE_NAME=api
          export ZED_LOAD_BALANCER_SIZE_UNIT=$ZED_API_LOAD_BALANCER_SIZE_UNIT
          export DATABASE_MAX_CONNECTIONS=60
          envsubst < crates/collab/k8s/collab.template.yml | kubectl apply -f -
          kubectl -n "$ZED_KUBE_NAMESPACE" rollout status deployment/$ZED_SERVICE_NAME --watch
          echo "deployed ${ZED_SERVICE_NAME} to ${ZED_KUBE_NAMESPACE}"
