name: Release Actions

on:
  release:
    types: [published]

jobs:
  discord_release:
    if: github.repository_owner == 'zed-industries'
    runs-on: ubuntu-latest
    steps:
      - name: Get release URL
        id: get-release-url
        run: |
          if [ "${{ github.event.release.prerelease }}" == "true" ]; then
              URL="https://zed.dev/releases/preview/latest"
          else
              URL="https://zed.dev/releases/stable/latest"
          fi

          echo "URL=$URL" >> "$GITHUB_OUTPUT"
      - name: Get content
        uses: 2428392/gh-truncate-string-action@b3ff790d21cf42af3ca7579146eedb93c8fb0757 # v1.4.1
        id: get-content
        with:
          stringToTruncate: |
            📣 Zed [${{ github.event.release.tag_name }}](<${{ steps.get-release-url.outputs.URL }}>) was just released!

            ${{ github.event.release.body }}
          maxLength: 2000
          truncationSymbol: "..."
      - name: Discord Webhook Action
        uses: tsickert/discord-webhook@c840d45a03a323fbc3f7507ac7769dbd91bfb164 # v5.3.0
        with:
          webhook-url: ${{ secrets.DISCORD_WEBHOOK_URL }}
          content: ${{ steps.get-content.outputs.string }}

  send_release_notes_email:
    if: github.repository_owner == 'zed-industries' && !github.event.release.prerelease
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          fetch-depth: 0

      - name: Check if release was promoted from preview
        id: check-promotion-from-preview
        run: |
          VERSION="${{ github.event.release.tag_name }}"
          PREVIEW_TAG="${VERSION}-pre"

          if git rev-parse "$PREVIEW_TAG" > /dev/null 2>&1; then
              echo "was_promoted_from_preview=true" >> "$GITHUB_OUTPUT"
          else
              echo "was_promoted_from_preview=false" >> "$GITHUB_OUTPUT"
          fi

      - name: Send release notes email
        if: steps.check-promotion-from-preview.outputs.was_promoted_from_preview == 'true'
        run: |
          TAG="${{ github.event.release.tag_name }}"
          cat << 'EOF' > release_body.txt
          ${{ github.event.release.body }}
          EOF
          jq -n --arg tag "$TAG" --rawfile body release_body.txt '{version: $tag, markdown_body: $body}' \
            > release_data.json
          curl -X POST "https://zed.dev/api/send_release_notes_email" \
            -H "Authorization: Bearer ${{ secrets.RELEASE_NOTES_API_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d @release_data.json
