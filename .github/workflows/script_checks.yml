name: <PERSON>ript

on:
  pull_request:
    paths:
      - "script/**"
  push:
    branches:
      - main

jobs:
  shellcheck:
    name: "ShellCheck Scripts"
    if: github.repository_owner == 'zed-industries'
    runs-on: namespace-profile-2x4-ubuntu-2404

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - name: Shellcheck ./scripts
        run: |
          ./script/shellcheck-scripts error
