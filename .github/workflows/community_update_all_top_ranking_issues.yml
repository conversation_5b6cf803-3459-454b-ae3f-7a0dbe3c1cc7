name: Update All Top Ranking Issues

on:
  schedule:
    - cron: "0 */12 * * *"
  workflow_dispatch:

jobs:
  update_top_ranking_issues:
    runs-on: ubuntu-latest
    if: github.repository == 'zed-industries/zed'
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - name: Set up uv
        uses: astral-sh/setup-uv@caf0cab7a618c569241d31dcd442f54681755d39 # v3
        with:
          version: "latest"
          enable-cache: true
          cache-dependency-glob: "script/update_top_ranking_issues/pyproject.toml"
      - name: Install Python 3.13
        run: uv python install 3.13
      - name: Install dependencies
        run: uv sync --project script/update_top_ranking_issues -p 3.13
      - name: Run script
        run: uv run --project script/update_top_ranking_issues script/update_top_ranking_issues/main.py --github-token ${{ secrets.GITHUB_TOKEN }} --issue-reference-number 5393
