name: CI

on:
  push:
    branches:
      - main
      - "v[0-9]+.[0-9]+.x"
    tags:
      - "v*"

  pull_request:
    branches:
      - "**"

concurrency:
  # Allow only one workflow per any non-`main` branch.
  group: ${{ github.workflow }}-${{ github.ref_name }}-${{ github.ref_name == 'main' && github.sha || 'anysha' }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always
  CARGO_INCREMENTAL: 0
  RUST_BACKTRACE: 1
  DIGITALOCEAN_SPACES_ACCESS_KEY: ${{ secrets.DIGITALOCEAN_SPACES_ACCESS_KEY }}
  DIGITALOCEAN_SPACES_SECRET_KEY: ${{ secrets.DIGITALOCEAN_SPACES_SECRET_KEY }}
  ZED_CLIENT_CHECKSUM_SEED: ${{ secrets.ZED_CLIENT_CHECKSUM_SEED }}
  ZED_MINIDUMP_ENDPOINT: ${{ secrets.ZED_SENTRY_MINIDUMP_ENDPOINT }}

jobs:
  job_spec:
    name: Decide which jobs to run
    if: github.repository_owner == 'zed-industries'
    outputs:
      run_tests: ${{ steps.filter.outputs.run_tests }}
      run_license: ${{ steps.filter.outputs.run_license }}
      run_docs: ${{ steps.filter.outputs.run_docs }}
      run_nix: ${{ steps.filter.outputs.run_nix }}
      run_actionlint: ${{ steps.filter.outputs.run_actionlint }}
    runs-on:
      - namespace-profile-2x4-ubuntu-2404
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          # 350 is arbitrary; ~10days of history on main (5secs); full history is ~25secs
          fetch-depth: ${{ github.ref == 'refs/heads/main' && 2 || 350 }}
      - name: Fetch git history and generate output filters
        id: filter
        run: |
          if [ -z "$GITHUB_BASE_REF" ]; then
            echo "Not in a PR context (i.e., push to main/stable/preview)"
            COMPARE_REV="$(git rev-parse HEAD~1)"
          else
            echo "In a PR context comparing to pull_request.base.ref"
            git fetch origin "$GITHUB_BASE_REF" --depth=350
            COMPARE_REV="$(git merge-base "origin/${GITHUB_BASE_REF}" HEAD)"
          fi
          CHANGED_FILES="$(git diff --name-only "$COMPARE_REV" ${{ github.sha }})"

          # Specify anything which should potentially skip full test suite in this regex:
          # - docs/
          # - script/update_top_ranking_issues/
          # - .github/ISSUE_TEMPLATE/
          # - .github/workflows/  (except .github/workflows/ci.yml)
          SKIP_REGEX='^(docs/|script/update_top_ranking_issues/|\.github/(ISSUE_TEMPLATE|workflows/(?!ci)))'

          echo "$CHANGED_FILES" | grep -qvP "$SKIP_REGEX" && \
            echo "run_tests=true" >> "$GITHUB_OUTPUT" || \
            echo "run_tests=false" >> "$GITHUB_OUTPUT"

          echo "$CHANGED_FILES" | grep -qP '^docs/' && \
            echo "run_docs=true" >> "$GITHUB_OUTPUT" || \
            echo "run_docs=false" >> "$GITHUB_OUTPUT"

          echo "$CHANGED_FILES" | grep -qP '^\.github/(workflows/|actions/|actionlint.yml)' && \
            echo "run_actionlint=true" >> "$GITHUB_OUTPUT" || \
            echo "run_actionlint=false" >> "$GITHUB_OUTPUT"

          echo "$CHANGED_FILES" | grep -qP '^(Cargo.lock|script/.*licenses)' && \
            echo "run_license=true" >> "$GITHUB_OUTPUT" || \
            echo "run_license=false" >> "$GITHUB_OUTPUT"

          echo "$CHANGED_FILES" | grep -qP '^(nix/|flake\.|Cargo\.|rust-toolchain.toml|\.cargo/config.toml)' && \
            echo "run_nix=true" >> "$GITHUB_OUTPUT" || \
            echo "run_nix=false" >> "$GITHUB_OUTPUT"

  migration_checks:
    name: Check Postgres and Protobuf migrations, mergability
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    timeout-minutes: 60
    runs-on:
      - self-mini-macos
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false
          fetch-depth: 0 # fetch full history

      - name: Remove untracked files
        run: git clean -df

      - name: Find modified migrations
        shell: bash -euxo pipefail {0}
        run: |
          export SQUAWK_GITHUB_TOKEN=${{ github.token }}
          . ./script/squawk

      - name: Ensure fresh merge
        shell: bash -euxo pipefail {0}
        run: |
          if [ -z "$GITHUB_BASE_REF" ];
          then
            echo "BUF_BASE_BRANCH=$(git merge-base origin/main HEAD)" >> "$GITHUB_ENV"
          else
            git checkout -B temp
            git merge -q "origin/$GITHUB_BASE_REF" -m "merge main into temp"
            echo "BUF_BASE_BRANCH=$GITHUB_BASE_REF" >> "$GITHUB_ENV"
          fi

      - uses: bufbuild/buf-setup-action@v1
        with:
          version: v1.29.0
      - uses: bufbuild/buf-breaking-action@v1
        with:
          input: "crates/proto/proto/"
          against: "https://github.com/${GITHUB_REPOSITORY}.git#branch=${BUF_BASE_BRANCH},subdir=crates/proto/proto/"

  workspace_hack:
    timeout-minutes: 60
    name: Check workspace-hack crate
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - namespace-profile-8x16-ubuntu-2204
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - name: Add Rust to the PATH
        run: echo "$HOME/.cargo/bin" >> "$GITHUB_PATH"
      - name: Install cargo-hakari
        uses: clechasseur/rs-cargo@8435b10f6e71c2e3d4d3b7573003a8ce4bfc6386 # v2
        with:
          command: install
          args: cargo-hakari@0.9.35

      - name: Check workspace-hack Cargo.toml is up-to-date
        run: |
          cargo hakari generate --diff || {
            echo "To fix, run script/update-workspace-hack or script/update-workspace-hack.ps1";
            false
          }
      - name: Check all crates depend on workspace-hack
        run: |
          cargo hakari manage-deps --dry-run || {
            echo "To fix, run script/update-workspace-hack or script/update-workspace-hack.ps1"
            false
          }

  style:
    timeout-minutes: 60
    name: Check formatting and spelling
    needs: [job_spec]
    if: github.repository_owner == 'zed-industries'
    runs-on:
      - namespace-profile-4x8-ubuntu-2204
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - uses: pnpm/action-setup@fe02b34f77f8bc703788d5817da081398fad5dd2 # v4.0.0
        with:
          version: 9

      - name: Prettier Check on /docs
        working-directory: ./docs
        run: |
          pnpm dlx "prettier@${PRETTIER_VERSION}" . --check || {
            echo "To fix, run from the root of the Zed repo:"
            echo "  cd docs && pnpm dlx prettier@${PRETTIER_VERSION} . --write && cd .."
            false
          }
        env:
          PRETTIER_VERSION: 3.5.0

      - name: Prettier Check on default.json
        run: |
          pnpm dlx "prettier@${PRETTIER_VERSION}" assets/settings/default.json --check || {
            echo "To fix, run from the root of the Zed repo:"
            echo "  pnpm dlx prettier@${PRETTIER_VERSION} assets/settings/default.json --write"
            false
          }
        env:
          PRETTIER_VERSION: 3.5.0

      # To support writing comments that they will certainly be revisited.
      - name: Check for todo! and FIXME comments
        run: script/check-todos

      - name: Check modifier use in keymaps
        run: script/check-keymaps

      - name: Run style checks
        uses: ./.github/actions/check_style

      - name: Check for typos
        uses: crate-ci/typos@8e6a4285bcbde632c5d79900a7779746e8b7ea3f # v1.24.6
        with:
          config: ./typos.toml

  check_docs:
    timeout-minutes: 60
    name: Check docs
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      (needs.job_spec.outputs.run_tests == 'true' || needs.job_spec.outputs.run_docs == 'true')
    runs-on:
      - namespace-profile-8x16-ubuntu-2204
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: Build docs
        uses: ./.github/actions/build_docs

  actionlint:
    runs-on: namespace-profile-2x4-ubuntu-2404
    if: github.repository_owner == 'zed-industries' && needs.job_spec.outputs.run_actionlint == 'true'
    needs: [job_spec]
    steps:
      - uses: actions/checkout@v4
      - name: Download actionlint
        id: get_actionlint
        run: bash <(curl https://raw.githubusercontent.com/rhysd/actionlint/main/scripts/download-actionlint.bash)
        shell: bash
      - name: Check workflow files
        run: ${{ steps.get_actionlint.outputs.executable }} -color
        shell: bash

  macos_tests:
    timeout-minutes: 60
    name: (macOS) Run Clippy and tests
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - self-mini-macos
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: Check that Cargo.lock is up to date
        run: |
          cargo update --locked --workspace

      - name: cargo clippy
        run: ./script/clippy

      - name: Install cargo-machete
        uses: clechasseur/rs-cargo@8435b10f6e71c2e3d4d3b7573003a8ce4bfc6386 # v2
        with:
          command: install
          args: cargo-machete@0.7.0

      - name: Check unused dependencies
        uses: clechasseur/rs-cargo@8435b10f6e71c2e3d4d3b7573003a8ce4bfc6386 # v2
        with:
          command: machete

      - name: Check licenses
        run: |
          script/check-licenses
          if [[ "${{ needs.job_spec.outputs.run_license }}" == "true" ]]; then
            script/generate-licenses /tmp/zed_licenses_output
          fi

      - name: Check for new vulnerable dependencies
        if: github.event_name == 'pull_request'
        uses: actions/dependency-review-action@67d4f4bd7a9b17a0db54d2a7519187c65e339de8 # v4
        with:
          license-check: false

      - name: Run tests
        uses: ./.github/actions/run_tests

      - name: Build collab
        run: cargo build -p collab

      - name: Build other binaries and features
        run: |
          cargo build --workspace --bins --all-features
          cargo check -p gpui --features "macos-blade"
          cargo check -p workspace
          cargo build -p remote_server
          cargo check -p gpui --examples

      # Since the macOS runners are stateful, so we need to remove the config file to prevent potential bug.
      - name: Clean CI config file
        if: always()
        run: rm -rf ./../.cargo

  linux_tests:
    timeout-minutes: 60
    name: (Linux) Run Clippy and tests
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - namespace-profile-16x32-ubuntu-2204
    steps:
      - name: Add Rust to the PATH
        run: echo "$HOME/.cargo/bin" >> "$GITHUB_PATH"

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Cache dependencies
        uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          # cache-provider: "buildjet"

      - name: Install Linux dependencies
        run: ./script/linux

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: cargo clippy
        run: ./script/clippy

      - name: Run tests
        uses: ./.github/actions/run_tests

      - name: Build other binaries and features
        run: |
          cargo build -p zed
          cargo check -p workspace
          cargo check -p gpui --examples

      # Even the Linux runner is not stateful, in theory there is no need to do this cleanup.
      # But, to avoid potential issues in the future if we choose to use a stateful Linux runner and forget to add code
      # to clean up the config file, I’ve included the cleanup code here as a precaution.
      # While it’s not strictly necessary at this moment, I believe it’s better to err on the side of caution.
      - name: Clean CI config file
        if: always()
        run: rm -rf ./../.cargo

  build_remote_server:
    timeout-minutes: 60
    name: (Linux) Build Remote Server
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on:
      - namespace-profile-16x32-ubuntu-2204
    steps:
      - name: Add Rust to the PATH
        run: echo "$HOME/.cargo/bin" >> "$GITHUB_PATH"

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Cache dependencies
        uses: swatinem/rust-cache@9d47c6ad4b02e050fd481d890b2ea34778fd09d6 # v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          # cache-provider: "buildjet"

      - name: Install Clang & Mold
        run: ./script/remote-server && ./script/install-mold 2.34.0

      - name: Configure CI
        run: |
          mkdir -p ./../.cargo
          cp ./.cargo/ci-config.toml ./../.cargo/config.toml

      - name: Build Remote Server
        run: cargo build -p remote_server

      - name: Clean CI config file
        if: always()
        run: rm -rf ./../.cargo

  windows_tests:
    timeout-minutes: 60
    name: (Windows) Run Clippy and tests
    needs: [job_spec]
    if: |
      github.repository_owner == 'zed-industries' &&
      needs.job_spec.outputs.run_tests == 'true'
    runs-on: [self-32vcpu-windows-2022]
    steps:
      - name: Environment Setup
        run: |
          $RunnerDir = Split-Path -Parent $env:RUNNER_WORKSPACE
          Write-Output `
            "RUSTUP_HOME=$RunnerDir\.rustup" `
            "CARGO_HOME=$RunnerDir\.cargo" `
            "PATH=$RunnerDir\.cargo\bin;$env:PATH" `
          >> $env:GITHUB_ENV

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Configure CI
        run: |
          New-Item -ItemType Directory -Path "./../.cargo" -Force
          Copy-Item -Path "./.cargo/ci-config.toml" -Destination "./../.cargo/config.toml"

      - name: cargo clippy
        run: |
          .\script\clippy.ps1

      - name: Run tests
        uses: ./.github/actions/run_tests_windows

      - name: Build Zed
        run: cargo build

      - name: Limit target directory size
        run: ./script/clear-target-dir-if-larger-than.ps1 250

      - name: Clean CI config file
        if: always()
        run: Remove-Item -Recurse -Path "./../.cargo" -Force -ErrorAction SilentlyContinue

  tests_pass:
    name: Tests Pass
    runs-on: namespace-profile-2x4-ubuntu-2404
    needs:
      - job_spec
      - style
      - check_docs
      - actionlint
      - migration_checks
      # run_tests: If adding required tests, add them here and to script below.
      - workspace_hack
      - linux_tests
      - build_remote_server
      - macos_tests
      - windows_tests
    if: |
      github.repository_owner == 'zed-industries' &&
      always()
    steps:
      - name: Check all tests passed
        run: |
          # Check dependent jobs...
          RET_CODE=0
          # Always check style
          [[ "${{ needs.style.result }}"      != 'success' ]] && { RET_CODE=1; echo "style tests failed"; }

          if [[ "${{ needs.job_spec.outputs.run_docs }}" == "true" ]]; then
            [[ "${{ needs.check_docs.result }}" != 'success' ]] && { RET_CODE=1; echo "docs checks failed"; }
          fi

          if [[ "${{ needs.job_spec.outputs.run_actionlint }}" == "true" ]]; then
            [[ "${{ needs.actionlint.result }}" != 'success' ]] && { RET_CODE=1; echo "actionlint checks failed"; }
          fi

          # Only check test jobs if they were supposed to run
          if [[ "${{ needs.job_spec.outputs.run_tests }}" == "true" ]]; then
            [[ "${{ needs.workspace_hack.result }}"       != 'success' ]] && { RET_CODE=1; echo "Workspace Hack failed"; }
            [[ "${{ needs.macos_tests.result }}"          != 'success' ]] && { RET_CODE=1; echo "macOS tests failed"; }
            [[ "${{ needs.linux_tests.result }}"          != 'success' ]] && { RET_CODE=1; echo "Linux tests failed"; }
            [[ "${{ needs.windows_tests.result }}"        != 'success' ]] && { RET_CODE=1; echo "Windows tests failed"; }
            [[ "${{ needs.build_remote_server.result }}"  != 'success' ]] && { RET_CODE=1; echo "Remote server build failed"; }
            # This check is intentionally disabled. See: https://github.com/zed-industries/zed/pull/28431
            # [[ "${{ needs.migration_checks.result }}"     != 'success' ]] && { RET_CODE=1; echo "Migration Checks failed"; }
          fi
          if [[ "$RET_CODE" -eq 0 ]]; then
            echo "All tests passed successfully!"
          fi
          exit $RET_CODE

  bundle-mac:
    timeout-minutes: 120
    name: Create a macOS bundle
    runs-on:
      - self-mini-macos
    if: |
      ( startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling') )
    needs: [macos_tests]
    env:
      MACOS_CERTIFICATE: ${{ secrets.MACOS_CERTIFICATE }}
      MACOS_CERTIFICATE_PASSWORD: ${{ secrets.MACOS_CERTIFICATE_PASSWORD }}
      APPLE_NOTARIZATION_KEY: ${{ secrets.APPLE_NOTARIZATION_KEY }}
      APPLE_NOTARIZATION_KEY_ID: ${{ secrets.APPLE_NOTARIZATION_KEY_ID }}
      APPLE_NOTARIZATION_ISSUER_ID: ${{ secrets.APPLE_NOTARIZATION_ISSUER_ID }}
    steps:
      - name: Install Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: "18"

      - name: Setup Sentry CLI
        uses: matbour/setup-sentry-cli@3e938c54b3018bdd019973689ef984e033b0454b #v2
        with:
          token: ${{ SECRETS.SENTRY_AUTH_TOKEN }}

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          # We need to fetch more than one commit so that `script/draft-release-notes`
          # is able to diff between the current and previous tag.
          #
          # 25 was chosen arbitrarily.
          fetch-depth: 25
          clean: false
          ref: ${{ github.ref }}

      - name: Limit target directory size
        run: script/clear-target-dir-if-larger-than 100

      - name: Determine version and release channel
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel

      - name: Draft release notes
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: |
          mkdir -p target/
          # Ignore any errors that occur while drafting release notes to not fail the build.
          script/draft-release-notes "$RELEASE_VERSION" "$RELEASE_CHANNEL" > target/release-notes.md || true
          script/create-draft-release target/release-notes.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create macOS app bundle
        run: script/bundle-mac

      - name: Rename binaries
        if: ${{ github.ref == 'refs/heads/main' }} || contains(github.event.pull_request.labels.*.name, 'run-bundling') }}
        run: |
          mv target/aarch64-apple-darwin/release/Zed.dmg target/aarch64-apple-darwin/release/Zed-aarch64.dmg
          mv target/x86_64-apple-darwin/release/Zed.dmg target/x86_64-apple-darwin/release/Zed-x86_64.dmg

      - name: Upload app bundle (aarch64) to workflow run if main branch or specific label
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: ${{ github.ref == 'refs/heads/main' }} || contains(github.event.pull_request.labels.*.name, 'run-bundling') }}
        with:
          name: Zed_${{ github.event.pull_request.head.sha || github.sha }}-aarch64.dmg
          path: target/aarch64-apple-darwin/release/Zed-aarch64.dmg

      - name: Upload app bundle (x86_64) to workflow run if main branch or specific label
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: ${{ github.ref == 'refs/heads/main' }} || contains(github.event.pull_request.labels.*.name, 'run-bundling') }}
        with:
          name: Zed_${{ github.event.pull_request.head.sha || github.sha }}-x86_64.dmg
          path: target/x86_64-apple-darwin/release/Zed-x86_64.dmg

      - uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        name: Upload app bundle to release
        if: ${{ env.RELEASE_CHANNEL == 'preview' || env.RELEASE_CHANNEL == 'stable' }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            target/zed-remote-server-macos-x86_64.gz
            target/zed-remote-server-macos-aarch64.gz
            target/aarch64-apple-darwin/release/Zed-aarch64.dmg
            target/x86_64-apple-darwin/release/Zed-x86_64.dmg
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  bundle-linux-x86_x64:
    timeout-minutes: 60
    name: Linux x86_x64 release bundle
    runs-on:
      - namespace-profile-16x32-ubuntu-2004 # ubuntu 20.04 for minimal glibc
    if: |
      ( startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling') )
    needs: [linux_tests]
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Install Linux dependencies
        run: ./script/linux && ./script/install-mold 2.34.0

      - name: Setup Sentry CLI
        uses: matbour/setup-sentry-cli@3e938c54b3018bdd019973689ef984e033b0454b #v2
        with:
          token: ${{ SECRETS.SENTRY_AUTH_TOKEN }}

      - name: Determine version and release channel
        if: startsWith(github.ref, 'refs/tags/v')
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel

      - name: Create Linux .tar.gz bundle
        run: script/bundle-linux

      - name: Upload Artifact to Workflow - zed (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-${{ github.event.pull_request.head.sha || github.sha }}-x86_64-unknown-linux-gnu.tar.gz
          path: target/release/zed-*.tar.gz

      - name: Upload Artifact to Workflow - zed-remote-server (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-remote-server-${{ github.event.pull_request.head.sha || github.sha }}-x86_64-unknown-linux-gnu.gz
          path: target/zed-remote-server-linux-x86_64.gz

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            target/zed-remote-server-linux-x86_64.gz
            target/release/zed-linux-x86_64.tar.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  bundle-linux-aarch64: # this runs on ubuntu22.04
    timeout-minutes: 60
    name: Linux arm64 release bundle
    runs-on:
      - namespace-profile-8x32-ubuntu-2004-arm-m4 # ubuntu 20.04 for minimal glibc
    if: |
      startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling')
    needs: [linux_tests]
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Install Linux dependencies
        run: ./script/linux

      - name: Setup Sentry CLI
        uses: matbour/setup-sentry-cli@3e938c54b3018bdd019973689ef984e033b0454b #v2
        with:
          token: ${{ SECRETS.SENTRY_AUTH_TOKEN }}

      - name: Determine version and release channel
        if: startsWith(github.ref, 'refs/tags/v')
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel

      - name: Create and upload Linux .tar.gz bundles
        run: script/bundle-linux

      - name: Upload Artifact to Workflow - zed (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-${{ github.event.pull_request.head.sha || github.sha }}-aarch64-unknown-linux-gnu.tar.gz
          path: target/release/zed-*.tar.gz

      - name: Upload Artifact to Workflow - zed-remote-server (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-remote-server-${{ github.event.pull_request.head.sha || github.sha }}-aarch64-unknown-linux-gnu.gz
          path: target/zed-remote-server-linux-aarch64.gz

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            target/zed-remote-server-linux-aarch64.gz
            target/release/zed-linux-aarch64.tar.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  freebsd:
    timeout-minutes: 60
    runs-on: github-8vcpu-ubuntu-2404
    if: |
      false && ( startsWith(github.ref, 'refs/tags/v')
      || contains(github.event.pull_request.labels.*.name, 'run-bundling') )
    needs: [linux_tests]
    name: Build Zed on FreeBSD
    steps:
      - uses: actions/checkout@v4
      - name: Build FreeBSD remote-server
        id: freebsd-build
        uses: vmactions/freebsd-vm@c3ae29a132c8ef1924775414107a97cac042aad5 # v1.2.0
        with:
          usesh: true
          release: 13.5
          copyback: true
          prepare: |
            pkg install -y \
              bash curl jq git \
              rustup-init cmake-core llvm-devel-lite pkgconf protobuf # ibx11 alsa-lib rust-bindgen-cli
          run: |
            freebsd-version
            sysctl hw.model
            sysctl hw.ncpu
            sysctl hw.physmem
            sysctl hw.usermem
            git config --global --add safe.directory /home/<USER>/work/zed/zed
            rustup-init --profile minimal --default-toolchain none -y
            . "$HOME/.cargo/env"
            ./script/bundle-freebsd
            mkdir -p out/
            mv "target/zed-remote-server-freebsd-x86_64.gz" out/
            rm -rf target/
            cargo clean

      - name: Upload Artifact to Workflow - zed-remote-server (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: zed-remote-server-${{ github.event.pull_request.head.sha || github.sha }}-x86_64-unknown-freebsd.gz
          path: out/zed-remote-server-freebsd-x86_64.gz

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) }}
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: |
            out/zed-remote-server-freebsd-x86_64.gz
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  nix-build:
    name: Build with Nix
    uses: ./.github/workflows/nix.yml
    needs: [job_spec]
    if: github.repository_owner == 'zed-industries' &&
      (contains(github.event.pull_request.labels.*.name, 'run-nix') ||
      needs.job_spec.outputs.run_nix == 'true')
    secrets: inherit
    with:
      flake-output: debug
      # excludes the final package to only cache dependencies
      cachix-filter: "-zed-editor-[0-9.]*-nightly"

  bundle-windows-x64:
    timeout-minutes: 120
    name: Create a Windows installer
    runs-on: [self-32vcpu-windows-2022]
    if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
    # if: (startsWith(github.ref, 'refs/tags/v') || contains(github.event.pull_request.labels.*.name, 'run-bundling'))
    needs: [windows_tests]
    env:
      AZURE_TENANT_ID: ${{ secrets.AZURE_SIGNING_TENANT_ID }}
      AZURE_CLIENT_ID: ${{ secrets.AZURE_SIGNING_CLIENT_ID }}
      AZURE_CLIENT_SECRET: ${{ secrets.AZURE_SIGNING_CLIENT_SECRET }}
      ACCOUNT_NAME: ${{ vars.AZURE_SIGNING_ACCOUNT_NAME }}
      CERT_PROFILE_NAME: ${{ vars.AZURE_SIGNING_CERT_PROFILE_NAME }}
      ENDPOINT: ${{ vars.AZURE_SIGNING_ENDPOINT }}
      FILE_DIGEST: SHA256
      TIMESTAMP_DIGEST: SHA256
      TIMESTAMP_SERVER: "http://timestamp.acs.microsoft.com"
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Setup Sentry CLI
        uses: matbour/setup-sentry-cli@3e938c54b3018bdd019973689ef984e033b0454b #v2
        with:
          token: ${{ SECRETS.SENTRY_AUTH_TOKEN }}

      - name: Determine version and release channel
        working-directory: ${{ env.ZED_WORKSPACE }}
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: |
          # This exports RELEASE_CHANNEL into env (GITHUB_ENV)
          script/determine-release-channel.ps1

      - name: Build Zed installer
        working-directory: ${{ env.ZED_WORKSPACE }}
        run: script/bundle-windows.ps1

      - name: Upload installer (x86_64) to Workflow - zed (run-bundling)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: contains(github.event.pull_request.labels.*.name, 'run-bundling')
        with:
          name: ZedEditorUserSetup-x64-${{ github.event.pull_request.head.sha || github.sha }}.exe
          path: ${{ env.SETUP_PATH }}

      - name: Upload Artifacts to release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v1
        # Re-enable when we are ready to publish windows preview releases
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'run-bundling')) && env.RELEASE_CHANNEL == 'preview' }} # upload only preview
        with:
          draft: true
          prerelease: ${{ env.RELEASE_CHANNEL == 'preview' }}
          files: ${{ env.SETUP_PATH }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  auto-release-preview:
    name: Auto release preview
    if: |
      startsWith(github.ref, 'refs/tags/v')
      && endsWith(github.ref, '-pre') && !endsWith(github.ref, '.0-pre')
    needs: [bundle-mac, bundle-linux-x86_x64, bundle-linux-aarch64, bundle-windows-x64]
    runs-on:
      - self-mini-macos
    steps:
      - name: gh release
        run: gh release edit "$GITHUB_REF_NAME" --draft=false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create Sentry release
        uses: getsentry/action-release@526942b68292201ac6bbb99b9a0747d4abee354c # v3
        env:
          SENTRY_ORG: zed-dev
          SENTRY_PROJECT: zed
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        with:
          environment: production
