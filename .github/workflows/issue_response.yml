name: Issue Response

on:
  schedule:
    - cron: "0 12 * * 2"
  workflow_dispatch:

jobs:
  issue-response:
    if: github.repository_owner == 'zed-industries'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - uses: pnpm/action-setup@fe02b34f77f8bc703788d5817da081398fad5dd2 # v4.0.0
        with:
          version: 9

      - name: Setup Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: "20"
          cache: "pnpm"
          cache-dependency-path: "script/issue_response/pnpm-lock.yaml"

      - run: pnpm install --dir script/issue_response

      - name: Run Issue Response
        run: pnpm run --dir script/issue_response start
        env:
          ISSUE_RESPONSE_GITHUB_TOKEN: ${{ secrets.ISSUE_RESPONSE_GITHUB_TOKEN }}
          SLACK_ISSUE_RESPONSE_WEBHOOK_URL: ${{ secrets.SLACK_ISSUE_RESPONSE_WEBHOOK_URL }}
