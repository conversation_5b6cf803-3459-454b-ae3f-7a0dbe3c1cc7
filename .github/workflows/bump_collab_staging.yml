name: Bump collab-staging Tag

on:
  schedule:
    # Fire every day at 16:00 UTC (At the start of the US workday)
    - cron: "0 16 * * *"

jobs:
  update-collab-staging-tag:
    if: github.repository_owner == 'zed-industries'
    runs-on: namespace-profile-2x4-ubuntu-2404
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          fetch-depth: 0

      - name: Update collab-staging tag
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          git tag -f collab-staging
          git push origin collab-staging --force
