name: bump_patch_version

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch name to run on"
        required: true

concurrency:
  # Allow only one workflow per any non-`main` branch.
  group: ${{ github.workflow }}-${{ inputs.branch }}
  cancel-in-progress: true

jobs:
  bump_patch_version:
    if: github.repository_owner == 'zed-industries'
    runs-on:
      - namespace-profile-16x32-ubuntu-2204
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          ref: ${{ github.event.inputs.branch }}
          ssh-key: ${{ secrets.ZED_BOT_DEPLOY_KEY }}

      - name: Bump Patch Version
        run: |
          set -eux

          channel="$(cat crates/zed/RELEASE_CHANNEL)"

          tag_suffix=""
          case $channel in
            stable)
              ;;
            preview)
              tag_suffix="-pre"
              ;;
            *)
              echo "this must be run on either of stable|preview release branches" >&2
              exit 1
              ;;
          esac
          which cargo-set-version > /dev/null || cargo install cargo-edit
          output="$(cargo set-version -p zed --bump patch 2>&1 | sed 's/.* //')"
          export GIT_COMMITTER_NAME="Zed Bot"
          export GIT_COMMITTER_EMAIL="<EMAIL>"
          git commit -am "Bump to $output for @$GITHUB_ACTOR" --author "Zed Bot <<EMAIL>>"
          git tag "v${output}${tag_suffix}"
          git push origin HEAD "v${output}${tag_suffix}"
