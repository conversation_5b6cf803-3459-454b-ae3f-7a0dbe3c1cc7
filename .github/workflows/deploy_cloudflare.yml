name: Deploy Docs

on:
  push:
    branches:
      - main

jobs:
  deploy-docs:
    name: Deploy Docs
    if: github.repository_owner == 'zed-industries'
    runs-on: namespace-profile-16x32-ubuntu-2204

    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Set up default .cargo/config.toml
        run: cp ./.cargo/collab-config.toml ./.cargo/config.toml

      - name: Build docs
        uses: ./.github/actions/build_docs

      - name: Deploy Docs
        uses: cloudflare/wrangler-action@**************************************** # v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy target/deploy --project-name=docs

      - name: Deploy Install
        uses: cloudflare/wrangler-action@**************************************** # v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: r2 object put -f script/install.sh zed-open-source-website-assets/install.sh

      - name: Deploy Docs Workers
        uses: cloudflare/wrangler-action@**************************************** # v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy .cloudflare/docs-proxy/src/worker.js

      - name: Deploy Install Workers
        uses: cloudflare/wrangler-action@**************************************** # v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy .cloudflare/docs-proxy/src/worker.js

      - name: Preserve Wrangler logs
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        if: always()
        with:
          name: wrangler_logs
          path: /home/<USER>/.config/.wrangler/logs/
