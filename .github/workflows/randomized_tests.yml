name: Randomized Tests

concurrency: randomized-tests

on:
  push:
    branches:
      - randomized-tests-runner
  # schedule:
  #    - cron: '0 * * * *'

env:
  CARGO_TERM_COLOR: always
  CARGO_INCREMENTAL: 0
  RUST_BACKTRACE: 1
  ZED_SERVER_URL: https://zed.dev

jobs:
  tests:
    name: Run randomized tests
    if: github.repository_owner == 'zed-industries'
    runs-on:
      - namespace-profile-16x32-ubuntu-2204
    steps:
      - name: Install Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: "18"

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          clean: false

      - name: Run randomized tests
        run: script/randomized-test-ci
