name: Bug Report (AI)
description: Zed Agent Panel Bugs
type: "Bug"
labels: ["ai"]
title: "AI: <a short description of the AI Related bug>"
body:
  - type: textarea
    attributes:
      label: Summary
      description: Describe the bug with a one line summary, and provide detailed reproduction steps
      value: |
        <!-- Please insert a one line summary of the issue below -->
        SUMMARY_SENTENCE_HERE

        ### Description
        <!--  Describe with sufficient detail to reproduce from a clean Zed install. -->
        Steps to trigger the problem:
        1.
        2.
        3.

        **Expected Behavior**:
        **Actual Behavior**:

        ### Model Provider Details
        - Provider: (Anthropic via ZedPro, Anthropic via API key, Co<PERSON>lot Chat, Mistral, OpenAI, etc)
        - Model Name:
        - Mode: (Agent Panel, Inline Assistant, Terminal Assistant or Text Threads)
        - Other Details (MCPs, other settings, etc):
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Zed Version and System Specs
      description: 'Open Zed, and in the command palette select "zed: copy system specs into clipboard"'
      placeholder: |
        Output of "zed: copy system specs into clipboard"
    validations:
      required: true
