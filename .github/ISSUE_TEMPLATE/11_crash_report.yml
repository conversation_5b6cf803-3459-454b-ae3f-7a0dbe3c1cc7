name: Crash Report
description: <PERSON><PERSON> is Crashing or Hanging
type: "Crash"
body:
  - type: textarea
    attributes:
      label: Summary
      description: Summarize the issue with detailed reproduction steps
      value: |
        <!-- Begin your issue with a one sentence summary -->
        SUMMARY_SENTENCE_HERE

        ### Description
        <!-- Include all steps necessary to reproduce from a clean Zed installation. Be verbose -->
        Steps to trigger the problem:
        1.
        2.
        3.

        Actual Behavior:
        Expected Behavior:

    validations:
      required: true
  - type: textarea
    id: environment
    attributes:
      label: Zed Version and System Specs
      description: 'Open Zed, and in the command palette select "zed: copy system specs into clipboard"'
      placeholder: |
        Output of "zed: copy system specs into clipboard"
    validations:
      required: true
  - type: textarea
    attributes:
      label: If applicable, attach your `~/Library/Logs/Zed/Zed.log` file to this issue.
      description: |
        macOS: `~/Library/Logs/Zed/Zed.log`
        Linux: `~/.local/share/zed/logs/Zed.log` or $XDG_DATA_HOME
        If you only need the most recent lines, you can run the `zed: open log` command palette action to see the last 1000.
      value: |
        <details><summary>Zed.log</summary>

        <!-- Paste your log inside the code block. -->
        ```log

        ```

        </details>
    validations:
      required: false
