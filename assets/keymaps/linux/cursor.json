[
  // Cursor for MacOS. See: https://docs.cursor.com/kbd
  {
    "context": "Workspace",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-i": "agent::ToggleFocus",
      "ctrl-shift-i": "agent::ToggleFocus",
      "ctrl-l": "agent::ToggleFocus",
      "ctrl-shift-l": "agent::ToggleFocus",
      "ctrl-shift-j": "agent::OpenSettings"
    }
  },
  {
    "context": "Editor && mode == full",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-i": "agent::ToggleFocus",
      "ctrl-shift-i": "agent::ToggleFocus",
      "ctrl-shift-l": "agent::QuoteSelection", // In cursor uses "Ask" mode
      "ctrl-l": "agent::QuoteSelection", // In cursor uses "Agent" mode
      "ctrl-k": "assistant::Inline<PERSON><PERSON>",
      "ctrl-shift-k": "assistant::InsertIntoEditor"
    }
  },
  {
    "context": "InlineAssistEditor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-backspace": "editor::Cancel"
      // "alt-enter": // Quick Question
      // "ctrl-shift-enter": // Full File Context
      // "ctrl-shift-k": // Toggle input focus (editor <> inline assist)
    }
  },
  {
    "context": "AgentPanel || ContextEditor || (MessageEditor > Editor)",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-i": "workspace::ToggleRightDock",
      "ctrl-shift-i": "workspace::ToggleRightDock",
      "ctrl-l": "workspace::ToggleRightDock",
      "ctrl-shift-l": "workspace::ToggleRightDock",
      "ctrl-w": "workspace::ToggleRightDock", // technically should close chat
      "ctrl-.": "agent::ToggleProfileSelector",
      "ctrl-/": "agent::ToggleModelSelector",
      "ctrl-shift-backspace": "editor::Cancel",
      "ctrl-r": "agent::NewThread",
      "ctrl-shift-v": "editor::Paste",
      "ctrl-shift-k": "assistant::InsertIntoEditor"
      // "escape": "agent::ToggleFocus"
      ///// Enable when Zed supports multiple thread tabs
      // "ctrl-t": // new thread tab
      // "ctrl-[": // next thread tab
      // "ctrl-]": // next thread tab
      ///// Enable if Zed adds support for keyboard navigation of thread elements
      // "tab": // cycle to next message
      // "shift-tab": // cycle to previous message
    }
  },
  {
    "context": "Editor && editor_agent_diff",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "agent::KeepAll",
      "ctrl-backspace": "agent::RejectAll"
    }
  },
  {
    "context": "Editor && mode == full && edit_prediction",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-right": "editor::AcceptPartialEditPrediction"
    }
  },
  {
    "context": "Terminal",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-k": "assistant::InlineAssist"
    }
  }
]
