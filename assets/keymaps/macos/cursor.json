[
  // Cursor for MacOS. See: https://docs.cursor.com/kbd
  {
    "context": "Workspace",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-i": "agent::ToggleFocus",
      "cmd-shift-i": "agent::ToggleFocus",
      "cmd-l": "agent::ToggleFocus",
      "cmd-shift-l": "agent::ToggleFocus",
      "cmd-shift-j": "agent::OpenSettings"
    }
  },
  {
    "context": "Editor && mode == full",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-i": "agent::ToggleFocus",
      "cmd-shift-i": "agent::ToggleFocus",
      "cmd-shift-l": "agent::QuoteSelection", // In cursor uses "Ask" mode
      "cmd-l": "agent::QuoteSelection", // In cursor uses "Agent" mode
      "cmd-k": "assistant::Inline<PERSON><PERSON>",
      "cmd-shift-k": "assistant::InsertIntoEditor"
    }
  },
  {
    "context": "InlineAssistEditor",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-shift-backspace": "editor::Cancel",
      "cmd-enter": "menu::Confirm"
      // "alt-enter": // Quick Question
      // "cmd-shift-enter": // Full File Context
      // "cmd-shift-k": // Toggle input focus (editor <> inline assist)
    }
  },
  {
    "context": "AgentPanel || ContextEditor || (MessageEditor > Editor)",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-i": "workspace::ToggleRightDock",
      "cmd-shift-i": "workspace::ToggleRightDock",
      "cmd-l": "workspace::ToggleRightDock",
      "cmd-shift-l": "workspace::ToggleRightDock",
      "cmd-w": "workspace::ToggleRightDock", // technically should close chat
      "cmd-.": "agent::ToggleProfileSelector",
      "cmd-/": "agent::ToggleModelSelector",
      "cmd-shift-backspace": "editor::Cancel",
      "cmd-r": "agent::NewThread",
      "cmd-shift-v": "editor::Paste",
      "cmd-shift-k": "assistant::InsertIntoEditor"
      // "escape": "agent::ToggleFocus"
      ///// Enable when Zed supports multiple thread tabs
      // "cmd-t": // new thread tab
      // "cmd-[": // next thread tab
      // "cmd-]": // next thread tab
      ///// Enable if Zed adds support for keyboard navigation of thread elements
      // "tab": // cycle to next message
      // "shift-tab": // cycle to previous message
    }
  },
  {
    "context": "Editor && editor_agent_diff",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-enter": "agent::KeepAll",
      "cmd-backspace": "agent::RejectAll"
    }
  },
  {
    "context": "Editor && mode == full && edit_prediction",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-right": "editor::AcceptPartialEditPrediction"
    }
  },
  {
    "context": "Terminal",
    "use_key_equivalents": true,
    "bindings": {
      "cmd-k": "assistant::InlineAssist"
    }
  }
]
