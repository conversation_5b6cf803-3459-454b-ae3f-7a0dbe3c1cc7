// documentation: https://zed.dev/docs/key-bindings
//
// To see the default key bindings run `zed: open default keymap`
// from the command palette.
[
  {
    "bindings": {
      "ctrl-g": "menu::Cancel"
    }
  },
  {
    "context": "Editor",
    "bindings": {
      "ctrl-g": "editor::Cancel",
      "ctrl-x b": "tab_switcher::Toggle", // switch-to-buffer
      "alt-g g": "go_to_line::Toggle", // goto-line
      "alt-g alt-g": "go_to_line::Toggle", // goto-line
      "ctrl-space": "editor::SetMark", // set-mark
      "ctrl-@": "editor::SetMark", // set-mark
      "ctrl-x ctrl-x": "editor::SwapSelectionEnds", // exchange-point-and-mark
      "ctrl-f": "editor::MoveRight", // forward-char
      "ctrl-b": "editor::MoveLeft", // backward-char
      "ctrl-n": "editor::MoveDown", // next-line
      "ctrl-p": "editor::MoveUp", // previous-line
      "home": ["editor::MoveToBeginningOfLine", { "stop_at_soft_wraps": false }], // move-beginning-of-line
      "end": ["editor::MoveToEndOfLine", { "stop_at_soft_wraps": false }], // move-end-of-line
      "ctrl-a": ["editor::MoveToBeginningOfLine", { "stop_at_soft_wraps": false }], // move-beginning-of-line
      "ctrl-e": ["editor::MoveToEndOfLine", { "stop_at_soft_wraps": false }], // move-end-of-line
      "shift-home": ["editor::SelectToBeginningOfLine", { "stop_at_soft_wraps": false }], // move-beginning-of-line
      "shift-end": ["editor::SelectToEndOfLine", { "stop_at_soft_wraps": false }], // move-end-of-line
      "alt-m": ["editor::MoveToBeginningOfLine", { "stop_at_soft_wraps": false, "stop_at_indent": true }], // back-to-indentation
      "alt-f": "editor::MoveToNextSubwordEnd", // forward-word
      "alt-b": "editor::MoveToPreviousSubwordStart", // backward-word
      "alt-u": "editor::ConvertToUpperCase", // upcase-word
      "alt-l": "editor::ConvertToLowerCase", // downcase-word
      "alt-c": "editor::ConvertToUpperCamelCase", // capitalize-word
      "ctrl-t": "editor::Transpose", // transpose-chars
      "alt-;": ["editor::ToggleComments", { "advance_downwards": false }],
      "ctrl-x ctrl-;": "editor::ToggleComments",
      "alt-.": "editor::GoToDefinition", // xref-find-definitions
      "alt-?": "editor::FindAllReferences", // xref-find-references
      "alt-,": "pane::GoBack", // xref-pop-marker-stack
      "ctrl-x h": "editor::SelectAll", // mark-whole-buffer
      "ctrl-d": "editor::Delete", // delete-char
      "alt-d": "editor::DeleteToNextWordEnd", // kill-word
      "ctrl-k": "editor::KillRingCut", // kill-line
      "ctrl-w": "editor::Cut", // kill-region
      "alt-w": "editor::Copy", // kill-ring-save
      "ctrl-y": "editor::KillRingYank", // yank
      "ctrl-_": "editor::Undo", // undo
      "ctrl-/": "editor::Undo", // undo
      "ctrl-x u": "editor::Undo", // undo
      "alt-{": "editor::MoveToStartOfParagraph", // backward-paragraph
      "alt-}": "editor::MoveToEndOfParagraph", // forward-paragraph
      "ctrl-v": "editor::MovePageDown", // scroll-up
      "alt-v": "editor::MovePageUp", // scroll-down
      "ctrl-x [": "editor::MoveToBeginning", // beginning-of-buffer
      "ctrl-x ]": "editor::MoveToEnd", // end-of-buffer
      "alt-<": "editor::MoveToBeginning", // beginning-of-buffer
      "alt->": "editor::MoveToEnd", // end-of-buffer
      "ctrl-l": "editor::ScrollCursorCenterTopBottom", // recenter-top-bottom
      "ctrl-s": "buffer_search::Deploy", // isearch-forward
      "alt-^": "editor::JoinLines", // join-line
      "alt-q": "editor::Rewrap" // fill-paragraph
    }
  },
  {
    "context": "Editor && selection_mode", // region selection
    "bindings": {
      "right": "editor::SelectRight",
      "left": "editor::SelectLeft",
      "down": "editor::SelectDown",
      "up": "editor::SelectUp",
      "alt-left": "editor::SelectToPreviousWordStart",
      "alt-right": "editor::SelectToNextWordEnd",
      "pagedown": "editor::SelectPageDown",
      "ctrl-v": "editor::SelectPageDown",
      "pageup": "editor::SelectPageUp",
      "alt-v": "editor::SelectPageUp",
      "ctrl-f": "editor::SelectRight",
      "ctrl-b": "editor::SelectLeft",
      "ctrl-n": "editor::SelectDown",
      "ctrl-p": "editor::SelectUp",
      "home": ["editor::SelectToBeginningOfLine", { "stop_at_soft_wraps": false }],
      "end": ["editor::SelectToEndOfLine", { "stop_at_soft_wraps": false }],
      "ctrl-a": ["editor::SelectToBeginningOfLine", { "stop_at_soft_wraps": false }],
      "ctrl-e": ["editor::SelectToEndOfLine", { "stop_at_soft_wraps": false }],
      "alt-f": "editor::SelectToNextWordEnd",
      "alt-b": "editor::SelectToPreviousSubwordStart",
      "alt-<": "editor::SelectToBeginning",
      "alt->": "editor::SelectToEnd",
      "ctrl-g": "editor::Cancel"
    }
  },
  {
    "context": "Editor && (showing_code_actions || showing_completions)",
    "bindings": {
      "ctrl-p": "editor::ContextMenuPrevious",
      "ctrl-n": "editor::ContextMenuNext"
    }
  },
  {
    "context": "Editor && showing_signature_help && !showing_completions",
    "bindings": {
      "ctrl-p": "editor::SignatureHelpPrevious",
      "ctrl-n": "editor::SignatureHelpNext"
    }
  },
  {
    "context": "Workspace",
    "bindings": {
      "ctrl-x ctrl-c": "zed::Quit", // save-buffers-kill-terminal
      "ctrl-x 5 0": "workspace::CloseWindow", // delete-frame
      "ctrl-x 5 2": "workspace::NewWindow", // make-frame-command
      "ctrl-x o": "workspace::ActivateNextPane", // other-window
      "ctrl-x k": "pane::CloseActiveItem", // kill-buffer
      "ctrl-x 0": "pane::CloseActiveItem", // delete-window
      "ctrl-x 1": "pane::CloseOtherItems", // delete-other-windows
      "ctrl-x 2": "pane::SplitDown", // split-window-below
      "ctrl-x 3": "pane::SplitRight", // split-window-right
      "ctrl-x ctrl-f": "file_finder::Toggle", // find-file
      "ctrl-x ctrl-s": "workspace::Save", // save-buffer
      "ctrl-x ctrl-w": "workspace::SaveAs", // write-file
      "ctrl-x s": "workspace::SaveAll" // save-some-buffers
    }
  },
  {
    // Workaround to enable using emacs in the Zed terminal.
    // Unbind so Zed ignores these keys and lets emacs handle them.
    "context": "Terminal",
    "bindings": {
      "ctrl-x ctrl-c": null, // save-buffers-kill-terminal
      "ctrl-x ctrl-f": null, // find-file
      "ctrl-x ctrl-s": null, // save-buffer
      "ctrl-x ctrl-w": null, // write-file
      "ctrl-x s": null // save-some-buffers
    }
  },
  {
    "context": "BufferSearchBar > Editor",
    "bindings": {
      "ctrl-s": "search::SelectNextMatch",
      "ctrl-r": "search::SelectPreviousMatch",
      "ctrl-g": "buffer_search::Dismiss"
    }
  },
  {
    "context": "Pane",
    "bindings": {
      "ctrl-alt-left": "pane::GoBack",
      "ctrl-alt-right": "pane::GoForward"
    }
  }
]
