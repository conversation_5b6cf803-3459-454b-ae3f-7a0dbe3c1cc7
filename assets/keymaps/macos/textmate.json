[{"bindings": {"cmd-shift-o": "projects::OpenRecent", "cmd-alt-tab": "project_panel::ToggleFocus"}}, {"context": "Editor && mode == full", "bindings": {"cmd-l": "go_to_line::Toggle", "ctrl-shift-d": "editor::DuplicateLineDown", "cmd-b": "editor::GoToDefinition", "cmd-j": "editor::ScrollCursorCenter", "cmd-enter": "editor::<PERSON><PERSON><PERSON><PERSON><PERSON>", "cmd-alt-enter": "editor::NewlineAbove", "cmd-shift-l": "editor::SelectLine", "cmd-shift-t": "outline::Toggle"}}, {"context": "Editor", "bindings": {"alt-backspace": "editor::<PERSON><PERSON><PERSON><PERSON>P<PERSON>viousWordStart", "alt-shift-backspace": "editor::DeleteToNextWordEnd", "alt-delete": "editor::DeleteToNextWordEnd", "alt-shift-delete": "editor::DeleteToNextWordEnd", "ctrl-backspace": "editor::<PERSON><PERSON>ToPreviousSubwordStart", "ctrl-delete": "editor::<PERSON><PERSON>ToNextSubwordEnd", "alt-left": ["editor::MoveToPreviousWordStart", {"stop_at_soft_wraps": true}], "alt-right": ["editor::MoveToNextWordEnd", {"stop_at_soft_wraps": true}], "ctrl-left": "editor::MoveToPreviousSubwordStart", "ctrl-right": "editor::MoveToNextSubwordEnd", "cmd-shift-left": "editor::SelectToBeginningOfLine", "cmd-shift-right": "editor::SelectToEndOfLine", "alt-shift-left": ["editor::SelectToPreviousWordStart", {"stop_at_soft_wraps": true}], "alt-shift-right": ["editor::SelectToNextWordEnd", {"stop_at_soft_wraps": true}], "ctrl-shift-left": "editor::SelectToPreviousSubwordStart", "ctrl-shift-right": "editor::SelectToNextSubwordEnd", "ctrl-w": "editor::SelectNext", "ctrl-u": "editor::<PERSON><PERSON><PERSON><PERSON>UpperCase", "ctrl-shift-u": "editor::<PERSON><PERSON>ToLowerCase", "ctrl-alt-u": "editor::<PERSON><PERSON><PERSON><PERSON>UpperCamelCase", "ctrl-_": "editor::<PERSON><PERSON>ToSnakeCase"}}, {"context": "BufferSearchBar", "bindings": {"ctrl-s": "search::SelectNextMatch", "ctrl-shift-s": "search::SelectPreviousMatch"}}, {"context": "Workspace", "bindings": {"cmd-alt-ctrl-d": "workspace::ToggleLeftDock", "cmd-t": "file_finder::Toggle", "cmd-shift-t": "project_symbols::Toggle"}}, {"context": "Pane", "bindings": {"alt-cmd-r": "search::ToggleRegex", "ctrl-tab": "project_panel::ToggleFocus"}}, {"context": "ProjectPanel", "bindings": {"cmd-backspace": ["project_panel::<PERSON><PERSON>", {"skip_prompt": true}], "cmd-d": "project_panel::Duplicate", "cmd-n": "project_panel::NewDirectory", "return": "project_panel::<PERSON><PERSON>", "cmd-c": "project_panel::<PERSON><PERSON>", "cmd-v": "project_panel::<PERSON><PERSON>", "cmd-alt-c": "project_panel::<PERSON>py<PERSON><PERSON>"}}, {"context": "Dock", "bindings": {}}]