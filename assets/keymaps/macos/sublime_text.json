[
  {
    "bindings": {
      "cmd-{": "pane::ActivatePreviousItem",
      "cmd-}": "pane::ActivateNextItem",
      "ctrl-pageup": "pane::ActivatePreviousItem",
      "ctrl-pagedown": "pane::ActivateNextItem",
      "ctrl-1": ["workspace::ActivatePane", 0],
      "ctrl-2": ["workspace::ActivatePane", 1],
      "ctrl-3": ["workspace::ActivatePane", 2],
      "ctrl-4": ["workspace::ActivatePane", 3],
      "ctrl-5": ["workspace::ActivatePane", 4],
      "ctrl-6": ["workspace::ActivatePane", 5],
      "ctrl-7": ["workspace::ActivatePane", 6],
      "ctrl-8": ["workspace::ActivatePane", 7],
      "ctrl-9": ["workspace::ActivatePane", 8],
      "ctrl-!": ["workspace::MoveItemToPane", { "destination": 0, "focus": true }],
      "ctrl-@": ["workspace::MoveItemToPane", { "destination": 1 }],
      "ctrl-#": ["workspace::MoveItemToPane", { "destination": 2 }],
      "ctrl-$": ["workspace::MoveItemToPane", { "destination": 3 }],
      "ctrl-%": ["workspace::MoveItemToPane", { "destination": 4 }],
      "ctrl-^": ["workspace::MoveItemToPane", { "destination": 5 }],
      "ctrl-&": ["workspace::MoveItemToPane", { "destination": 6 }],
      "ctrl-*": ["workspace::MoveItemToPane", { "destination": 7 }],
      "ctrl-(": ["workspace::MoveItemToPane", { "destination": 8 }]
    }
  },
  {
    "context": "Editor",
    "bindings": {
      "ctrl-shift-up": "editor::AddSelectionAbove",
      "ctrl-shift-down": "editor::AddSelectionBelow",
      "cmd-ctrl-up": "editor::MoveLineUp",
      "cmd-ctrl-down": "editor::MoveLineDown",
      "cmd-shift-space": "editor::SelectAll",
      "ctrl-shift-m": "editor::SelectLargerSyntaxNode",
      "cmd-shift-l": "editor::SplitSelectionIntoLines",
      "cmd-shift-a": "editor::SelectLargerSyntaxNode",
      "cmd-shift-d": "editor::DuplicateSelection",
      "ctrl-cmd-g": "editor::SelectAllMatches", // find_all_under
      // "cmd-alt-g": "", // find_under (cancels any selections)
      // "cmd-alt-shift-g": "" // find_under_prev (cancels any selections)
      "f5": "editor::SortLinesCaseSensitive",
      "ctrl-f5": "editor::SortLinesCaseInsensitive",
      "shift-f12": "editor::FindAllReferences",
      "alt-cmd-down": "editor::GoToDefinition",
      "ctrl-alt-cmd-down": "editor::GoToDefinitionSplit",
      "alt-shift-cmd-down": "editor::FindAllReferences",
      "ctrl-.": "editor::GoToHunk",
      "ctrl-,": "editor::GoToPreviousHunk",
      "cmd-k cmd-u": "editor::ConvertToUpperCase",
      "cmd-k cmd-l": "editor::ConvertToLowerCase",
      "cmd-shift-j": "editor::JoinLines",
      "shift-alt-m": "markdown::OpenPreviewToTheSide",
      "ctrl-backspace": "editor::DeleteToPreviousWordStart",
      "ctrl-delete": "editor::DeleteToNextWordEnd",
      "ctrl-right": "editor::MoveToNextSubwordEnd",
      "ctrl-left": "editor::MoveToPreviousSubwordStart",
      "ctrl-shift-right": "editor::SelectToNextSubwordEnd",
      "ctrl-shift-left": "editor::SelectToPreviousSubwordStart"
    }
  },
  {
    "context": "Editor && mode == full",
    "bindings": {
      "cmd-r": "outline::Toggle"
    }
  },
  {
    "context": "Editor && !agent_diff",
    "bindings": {
      "cmd-k cmd-z": "git::Restore"
    }
  },
  {
    "context": "Pane",
    "bindings": {
      "f4": "search::SelectNextMatch",
      "shift-f4": "search::SelectPreviousMatch",
      "cmd-1": ["pane::ActivateItem", 0],
      "cmd-2": ["pane::ActivateItem", 1],
      "cmd-3": ["pane::ActivateItem", 2],
      "cmd-4": ["pane::ActivateItem", 3],
      "cmd-5": ["pane::ActivateItem", 4],
      "cmd-6": ["pane::ActivateItem", 5],
      "cmd-7": ["pane::ActivateItem", 6],
      "cmd-8": ["pane::ActivateItem", 7],
      "cmd-9": "pane::ActivateLastItem"
    }
  },
  {
    "context": "Workspace",
    "bindings": {
      "cmd-k cmd-b": "workspace::ToggleLeftDock",
      "cmd-t": "file_finder::Toggle",
      "shift-cmd-r": "project_symbols::Toggle",
      // Currently busted: https://github.com/zed-industries/feedback/issues/898
      "ctrl-0": "project_panel::ToggleFocus"
    }
  }
]
