[
  {
    "bindings": {
      "cmd-{": "pane::ActivatePreviousItem",
      "cmd-}": "pane::ActivateNextItem",
      "cmd-0": "git_panel::ToggleFocus", // overrides `cmd-0` zoom reset
      "shift-escape": null, // Unmap workspace::zoom
      "ctrl-f2": "debugger::Stop",
      "f6": "debugger::Pause",
      "f7": "debugger::StepInto",
      "f8": "debugger::StepOver",
      "shift-f8": "debugger::StepOut",
      "f9": "debugger::Continue",
      "alt-shift-f9": "debugger::Start"
    }
  },
  {
    "context": "Editor",
    "bindings": {
      "ctrl->": ["zed::IncreaseBufferFontSize", { "persist": true }],
      "ctrl-<": ["zed::DecreaseBufferFontSize", { "persist": true }],
      "ctrl-shift-j": "editor::JoinLines",
      "cmd-d": "editor::DuplicateSelection",
      "cmd-backspace": "editor::DeleteLine",
      "cmd-pagedown": "editor::MovePageDown",
      "cmd-pageup": "editor::MovePageUp",
      "ctrl-alt-shift-b": "editor::SelectToPreviousWordStart",
      "cmd-alt-enter": "editor::NewlineAbove",
      "shift-enter": "editor::NewlineBelow",
      "cmd--": "editor::Fold",
      "cmd-+": "editor::UnfoldLines",
      "alt-shift-g": "editor::SplitSelectionIntoLines",
      "ctrl-g": ["editor::SelectNext", { "replace_newest": false }],
      "ctrl-cmd-g": ["editor::SelectPrevious", { "replace_newest": false }],
      "cmd-/": ["editor::ToggleComments", { "advance_downwards": true }],
      "alt-up": "editor::SelectLargerSyntaxNode",
      "alt-down": "editor::SelectSmallerSyntaxNode",
      "shift-alt-up": "editor::MoveLineUp",
      "shift-alt-down": "editor::MoveLineDown",
      "cmd-alt-l": "editor::Format",
      "ctrl-alt-o": "editor::OrganizeImports",
      "shift-f6": "editor::Rename",
      "cmd-[": "pane::GoBack",
      "cmd-]": "pane::GoForward",
      "alt-f7": "editor::FindAllReferences",
      "cmd-alt-f7": "editor::FindAllReferences",
      "cmd-b": "editor::GoToDefinition", // Conflicts with workspace::ToggleLeftDock
      "cmd-alt-b": "editor::GoToDefinitionSplit",
      "cmd-shift-b": "editor::GoToTypeDefinition",
      "cmd-alt-shift-b": "editor::GoToTypeDefinitionSplit",
      "f2": "editor::GoToDiagnostic",
      "shift-f2": "editor::GoToPreviousDiagnostic",
      "ctrl-alt-shift-down": "editor::GoToHunk",
      "ctrl-alt-shift-up": "editor::GoToPreviousHunk",
      "cmd-home": "editor::MoveToBeginning",
      "cmd-end": "editor::MoveToEnd",
      "cmd-shift-home": "editor::SelectToBeginning",
      "cmd-shift-end": "editor::SelectToEnd",
      "ctrl-f8": "editor::ToggleBreakpoint",
      "ctrl-shift-f8": "editor::EditLogBreakpoint",
      "cmd-shift-u": "editor::ToggleCase"
    }
  },
  {
    "context": "Editor && mode == full",
    "bindings": {
      "cmd-f12": "outline::Toggle",
      "cmd-r": ["buffer_search::Deploy", { "replace_enabled": true }],
      "cmd-shift-o": "file_finder::Toggle",
      "cmd-l": "go_to_line::Toggle",
      "alt-enter": "editor::ToggleCodeActions"
    }
  },
  {
    "context": "BufferSearchBar",
    "bindings": {
      "shift-enter": "search::SelectPreviousMatch"
    }
  },
  {
    "context": "BufferSearchBar || ProjectSearchBar",
    "bindings": {
      "alt-c": "search::ToggleCaseSensitive",
      "alt-e": "search::ToggleSelection",
      "alt-x": "search::ToggleRegex",
      "alt-w": "search::ToggleWholeWord",
      "ctrl-alt-c": "search::ToggleCaseSensitive",
      "ctrl-alt-e": "search::ToggleSelection",
      "ctrl-alt-w": "search::ToggleWholeWord",
      "ctrl-alt-x": "search::ToggleRegex"
    }
  },
  {
    "context": "Workspace",
    "bindings": {
      "cmd-shift-f12": "workspace::CloseAllDocks",
      "cmd-shift-r": ["pane::DeploySearch", { "replace_enabled": true }],
      "ctrl-alt-r": "task::Spawn",
      "cmd-e": "file_finder::Toggle",
      // "cmd-k": "git_panel::ToggleFocus", // bug: This should also focus commit editor
      "cmd-shift-o": "file_finder::Toggle",
      "cmd-shift-a": "command_palette::Toggle",
      "shift shift": "command_palette::Toggle",
      "cmd-alt-o": "project_symbols::Toggle", // JetBrains: Go to Symbol
      "cmd-o": "project_symbols::Toggle", // JetBrains: Go to Class
      "cmd-1": "project_panel::ToggleFocus",
      "cmd-5": "debug_panel::ToggleFocus",
      "cmd-6": "diagnostics::Deploy",
      "cmd-7": "outline_panel::ToggleFocus"
    }
  },
  {
    "context": "Pane", // this is to override the default Pane mappings to switch tabs
    "bindings": {
      "cmd-1": "project_panel::ToggleFocus",
      "cmd-2": null, // Bookmarks (left dock)
      "cmd-3": null, // Find Panel (bottom dock)
      "cmd-4": null, // Run Panel (bottom dock)
      "cmd-5": "debug_panel::ToggleFocus",
      "cmd-6": "diagnostics::Deploy",
      "cmd-7": "outline_panel::ToggleFocus",
      "cmd-8": null, // Services (bottom dock)
      "cmd-9": null, // Git History (bottom dock)
      "cmd-0": "git_panel::ToggleFocus"
    }
  },
  {
    "context": "Workspace || Editor",
    "bindings": {
      "alt-f12": "terminal_panel::ToggleFocus",
      "cmd-shift-k": "git::Push"
    }
  },
  {
    "context": "Pane",
    "bindings": {
      "cmd-alt-left": "pane::GoBack",
      "cmd-alt-right": "pane::GoForward"
    }
  },
  {
    "context": "ProjectPanel",
    "bindings": {
      "enter": "project_panel::Open",
      "cmd-shift-f": "project_panel::NewSearchInDirectory",
      "cmd-backspace": ["project_panel::Trash", { "skip_prompt": false }],
      "backspace": ["project_panel::Trash", { "skip_prompt": false }],
      "delete": ["project_panel::Trash", { "skip_prompt": false }],
      "shift-delete": ["project_panel::Delete", { "skip_prompt": false }],
      "shift-f6": "project_panel::Rename"
    }
  },
  {
    "context": "Terminal",
    "bindings": {
      "cmd-t": "workspace::NewTerminal",
      "alt-f12": "workspace::CloseActiveDock",
      "cmd-up": "terminal::ScrollLineUp",
      "cmd-down": "terminal::ScrollLineDown",
      "shift-pageup": "terminal::ScrollPageUp",
      "shift-pagedown": "terminal::ScrollPageDown"
    }
  },
  { "context": "GitPanel", "bindings": { "cmd-0": "workspace::CloseActiveDock" } },
  { "context": "ProjectPanel", "bindings": { "cmd-1": "workspace::CloseActiveDock" } },
  { "context": "DebugPanel", "bindings": { "cmd-5": "workspace::CloseActiveDock" } },
  { "context": "Diagnostics > Editor", "bindings": { "cmd-6": "pane::CloseActiveItem" } },
  { "context": "OutlinePanel", "bindings": { "cmd-7": "workspace::CloseActiveDock" } },
  {
    "context": "Dock || Workspace || OutlinePanel || ProjectPanel || CollabPanel || (Editor && mode == auto_height)",
    "bindings": {
      "escape": "editor::ToggleFocus",
      "shift-escape": "workspace::CloseActiveDock"
    }
  }
]
