[
  // Standard Windows bindings
  {
    "use_key_equivalents": true,
    "bindings": {
      "home": "menu::SelectFirst",
      "shift-pageup": "menu::SelectFirst",
      "pageup": "menu::SelectFirst",
      "end": "menu::SelectLast",
      "shift-pagedown": "menu::SelectLast",
      "pagedown": "menu::SelectLast",
      "ctrl-n": "menu::SelectNext",
      "tab": "menu::SelectNext",
      "down": "menu::SelectNext",
      "ctrl-p": "menu::SelectPrevious",
      "shift-tab": "menu::SelectPrevious",
      "up": "menu::SelectPrevious",
      "enter": "menu::Confirm",
      "ctrl-enter": "menu::SecondaryConfirm",
      "ctrl-escape": "menu::Cancel",
      "ctrl-c": "menu::Cancel",
      "escape": "menu::Cancel",
      "shift-alt-enter": "menu::Restart",
      "alt-enter": ["picker::ConfirmInput", { "secondary": false }],
      "ctrl-alt-enter": ["picker::ConfirmInput", { "secondary": true }],
      "ctrl-shift-w": "workspace::CloseWindow",
      "shift-escape": "workspace::ToggleZoom",
      "open": "workspace::Open",
      "ctrl-o": "workspace::Open",
      "ctrl-=": ["zed::IncreaseBufferFontSize", { "persist": false }],
      "ctrl-shift-=": ["zed::IncreaseBufferFontSize", { "persist": false }],
      "ctrl--": ["zed::DecreaseBufferFontSize", { "persist": false }],
      "ctrl-0": ["zed::ResetBufferFontSize", { "persist": false }],
      "ctrl-,": "zed::OpenSettings",
      "ctrl-q": "zed::Quit",
      "f4": "debugger::Start",
      "shift-f5": "debugger::Stop",
      "ctrl-shift-f5": "debugger::RerunSession",
      "f6": "debugger::Pause",
      "f7": "debugger::StepOver",
      "ctrl-f11": "debugger::StepInto",
      "shift-f11": "debugger::StepOut",
      "f11": "zed::ToggleFullScreen",
      "ctrl-shift-i": "edit_prediction::ToggleMenu",
      "shift-alt-l": "lsp_tool::ToggleMenu"
    }
  },
  {
    "context": "Picker || menu",
    "use_key_equivalents": true,
    "bindings": {
      "up": "menu::SelectPrevious",
      "down": "menu::SelectNext"
    }
  },
  {
    "context": "Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "editor::Cancel",
      "shift-backspace": "editor::Backspace",
      "backspace": "editor::Backspace",
      "delete": "editor::Delete",
      "tab": "editor::Tab",
      "shift-tab": "editor::Backtab",
      "ctrl-k": "editor::CutToEndOfLine",
      "ctrl-k ctrl-q": "editor::Rewrap",
      "ctrl-k q": "editor::Rewrap",
      "ctrl-backspace": "editor::DeleteToPreviousWordStart",
      "ctrl-delete": "editor::DeleteToNextWordEnd",
      "cut": "editor::Cut",
      "shift-delete": "editor::Cut",
      "ctrl-x": "editor::Cut",
      "copy": "editor::Copy",
      "ctrl-insert": "editor::Copy",
      "ctrl-c": "editor::Copy",
      "paste": "editor::Paste",
      "shift-insert": "editor::Paste",
      "ctrl-v": "editor::Paste",
      "undo": "editor::Undo",
      "ctrl-z": "editor::Undo",
      "redo": "editor::Redo",
      "ctrl-y": "editor::Redo",
      "ctrl-shift-z": "editor::Redo",
      "up": "editor::MoveUp",
      "ctrl-up": "editor::LineUp",
      "ctrl-down": "editor::LineDown",
      "pageup": "editor::MovePageUp",
      "alt-pageup": "editor::PageUp",
      "shift-pageup": "editor::SelectPageUp",
      "home": ["editor::MoveToBeginningOfLine", { "stop_at_soft_wraps": true, "stop_at_indent": true }],
      "down": "editor::MoveDown",
      "pagedown": "editor::MovePageDown",
      "alt-pagedown": "editor::PageDown",
      "shift-pagedown": "editor::SelectPageDown",
      "end": ["editor::MoveToEndOfLine", { "stop_at_soft_wraps": true }],
      "left": "editor::MoveLeft",
      "right": "editor::MoveRight",
      "ctrl-left": "editor::MoveToPreviousWordStart",
      "ctrl-right": "editor::MoveToNextWordEnd",
      "ctrl-home": "editor::MoveToBeginning",
      "ctrl-end": "editor::MoveToEnd",
      "shift-up": "editor::SelectUp",
      "shift-down": "editor::SelectDown",
      "shift-left": "editor::SelectLeft",
      "shift-right": "editor::SelectRight",
      "ctrl-shift-left": "editor::SelectToPreviousWordStart",
      "ctrl-shift-right": "editor::SelectToNextWordEnd",
      "ctrl-shift-home": "editor::SelectToBeginning",
      "ctrl-shift-end": "editor::SelectToEnd",
      "ctrl-a": "editor::SelectAll",
      "ctrl-l": "editor::SelectLine",
      "shift-alt-f": "editor::Format",
      "shift-alt-o": "editor::OrganizeImports",
      "shift-home": ["editor::SelectToBeginningOfLine", { "stop_at_soft_wraps": true, "stop_at_indent": true }],
      "shift-end": ["editor::SelectToEndOfLine", { "stop_at_soft_wraps": true }],
      "ctrl-alt-space": "editor::ShowCharacterPalette",
      "ctrl-;": "editor::ToggleLineNumbers",
      "ctrl-'": "editor::ToggleSelectedDiffHunks",
      "ctrl-\"": "editor::ExpandAllDiffHunks",
      "ctrl-i": "editor::ShowSignatureHelp",
      "alt-g b": "git::Blame",
      "alt-g m": "git::OpenModifiedFiles",
      "menu": "editor::OpenContextMenu",
      "shift-f10": "editor::OpenContextMenu",
      "ctrl-shift-e": "editor::ToggleEditPrediction",
      "f9": "editor::ToggleBreakpoint",
      "shift-f9": "editor::EditLogBreakpoint"
    }
  },
  {
    "context": "Editor && mode == full",
    "use_key_equivalents": true,
    "bindings": {
      "shift-enter": "editor::Newline",
      "enter": "editor::Newline",
      "ctrl-enter": "editor::NewlineBelow",
      "ctrl-shift-enter": "editor::NewlineAbove",
      "ctrl-k ctrl-z": "editor::ToggleSoftWrap",
      "ctrl-k z": "editor::ToggleSoftWrap",
      "find": "buffer_search::Deploy",
      "ctrl-f": "buffer_search::Deploy",
      "ctrl-h": "buffer_search::DeployReplace",
      "ctrl-shift-.": "assistant::QuoteSelection",
      "ctrl-shift-,": "assistant::InsertIntoEditor",
      "shift-alt-e": "editor::SelectEnclosingSymbol",
      "ctrl-shift-backspace": "editor::GoToPreviousChange",
      "ctrl-shift-alt-backspace": "editor::GoToNextChange",
      "alt-enter": "editor::OpenSelectionsInMultibuffer"
    }
  },
  {
    "context": "Editor && mode == full && edit_prediction",
    "use_key_equivalents": true,
    "bindings": {
      "alt-]": "editor::NextEditPrediction",
      "alt-[": "editor::PreviousEditPrediction"
    }
  },
  {
    "context": "Editor && !edit_prediction",
    "use_key_equivalents": true,
    "bindings": {
      "alt-\\": "editor::ShowEditPrediction"
    }
  },
  {
    "context": "Editor && mode == auto_height",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "editor::Newline",
      "shift-enter": "editor::Newline",
      "ctrl-shift-enter": "editor::NewlineBelow"
    }
  },
  {
    "context": "Markdown",
    "use_key_equivalents": true,
    "bindings": {
      "copy": "markdown::Copy",
      "ctrl-c": "markdown::Copy"
    }
  },
  {
    "context": "Editor && jupyter && !ContextEditor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-enter": "repl::Run",
      "ctrl-alt-enter": "repl::RunInPlace"
    }
  },
  {
    "context": "Editor && !agent_diff",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-k ctrl-r": "git::Restore",
      "alt-y": "git::StageAndNext",
      "shift-alt-y": "git::UnstageAndNext"
    }
  },
  {
    "context": "Editor && editor_agent_diff",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-y": "agent::Keep",
      "ctrl-n": "agent::Reject",
      "ctrl-shift-y": "agent::KeepAll",
      "ctrl-shift-n": "agent::RejectAll",
      "ctrl-shift-r": "agent::OpenAgentDiff"
    }
  },
  {
    "context": "AgentDiff",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-y": "agent::Keep",
      "ctrl-n": "agent::Reject",
      "ctrl-shift-y": "agent::KeepAll",
      "ctrl-shift-n": "agent::RejectAll"
    }
  },
  {
    "context": "ContextEditor > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "assistant::Assist",
      "ctrl-s": "workspace::Save",
      "save": "workspace::Save",
      "ctrl-shift-,": "assistant::InsertIntoEditor",
      "shift-enter": "assistant::Split",
      "ctrl-r": "assistant::CycleMessageRole",
      "enter": "assistant::ConfirmCommand",
      "alt-enter": "editor::Newline",
      "ctrl-k c": "assistant::CopyCode",
      "ctrl-g": "search::SelectNextMatch",
      "ctrl-shift-g": "search::SelectPreviousMatch",
      "ctrl-k l": "agent::OpenRulesLibrary"
    }
  },
  {
    "context": "AgentPanel",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-n": "agent::NewThread",
      "shift-alt-n": "agent::NewTextThread",
      "ctrl-shift-h": "agent::OpenHistory",
      "shift-alt-c": "agent::OpenSettings",
      "shift-alt-p": "agent::OpenRulesLibrary",
      "ctrl-i": "agent::ToggleProfileSelector",
      "shift-alt-/": "agent::ToggleModelSelector",
      "ctrl-shift-a": "agent::ToggleContextPicker",
      "ctrl-shift-j": "agent::ToggleNavigationMenu",
      "ctrl-shift-i": "agent::ToggleOptionsMenu",
      // "ctrl-shift-alt-n": "agent::ToggleNewThreadMenu",
      "shift-alt-escape": "agent::ExpandMessageEditor",
      "ctrl-shift-.": "assistant::QuoteSelection",
      "shift-alt-e": "agent::RemoveAllContext",
      "ctrl-shift-e": "project_panel::ToggleFocus",
      "ctrl-shift-enter": "agent::ContinueThread",
      "super-ctrl-b": "agent::ToggleBurnMode",
      "alt-enter": "agent::ContinueWithBurnMode"
    }
  },
  {
    "context": "AgentPanel > NavigationMenu",
    "use_key_equivalents": true,
    "bindings": {
      "shift-backspace": "agent::DeleteRecentlyOpenThread"
    }
  },
  {
    "context": "AgentPanel > Markdown",
    "use_key_equivalents": true,
    "bindings": {
      "copy": "markdown::CopyAsMarkdown",
      "ctrl-c": "markdown::CopyAsMarkdown"
    }
  },
  {
    "context": "AgentPanel && prompt_editor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-n": "agent::NewTextThread",
      "ctrl-alt-t": "agent::NewThread"
    }
  },
  {
    "context": "AgentPanel && external_agent_thread",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-n": "agent::NewExternalAgentThread",
      "ctrl-alt-t": "agent::NewThread"
    }
  },
  {
    "context": "MessageEditor && !Picker > Editor && !use_modifier_to_send",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "agent::Chat",
      "ctrl-enter": "agent::ChatWithFollow",
      "ctrl-i": "agent::ToggleProfileSelector",
      "ctrl-shift-r": "agent::OpenAgentDiff",
      "ctrl-shift-y": "agent::KeepAll",
      "ctrl-shift-n": "agent::RejectAll"
    }
  },
  {
    "context": "MessageEditor && !Picker > Editor && use_modifier_to_send",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "agent::Chat",
      "enter": "editor::Newline",
      "ctrl-i": "agent::ToggleProfileSelector",
      "ctrl-shift-r": "agent::OpenAgentDiff",
      "ctrl-shift-y": "agent::KeepAll",
      "ctrl-shift-n": "agent::RejectAll"
    }
  },
  {
    "context": "EditMessageEditor > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel",
      "enter": "menu::Confirm",
      "alt-enter": "editor::Newline"
    }
  },
  {
    "context": "AgentFeedbackMessageEditor > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel",
      "enter": "menu::Confirm",
      "alt-enter": "editor::Newline"
    }
  },
  {
    "context": "ContextStrip",
    "use_key_equivalents": true,
    "bindings": {
      "up": "agent::FocusUp",
      "right": "agent::FocusRight",
      "left": "agent::FocusLeft",
      "down": "agent::FocusDown",
      "backspace": "agent::RemoveFocusedContext",
      "enter": "agent::AcceptSuggestedContext"
    }
  },
  {
    "context": "AcpThread > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "agent::Chat",
      "ctrl-shift-r": "agent::OpenAgentDiff",
      "ctrl-shift-y": "agent::KeepAll",
      "ctrl-shift-n": "agent::RejectAll"
    }
  },
  {
    "context": "ThreadHistory",
    "use_key_equivalents": true,
    "bindings": {
      "backspace": "agent::RemoveSelectedThread"
    }
  },
  {
    "context": "PromptLibrary",
    "use_key_equivalents": true,
    "bindings": {
      "new": "rules_library::NewRule",
      "ctrl-n": "rules_library::NewRule",
      "ctrl-shift-s": "rules_library::ToggleDefaultRule"
    }
  },
  {
    "context": "BufferSearchBar",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "buffer_search::Dismiss",
      "tab": "buffer_search::FocusEditor",
      "enter": "search::SelectNextMatch",
      "shift-enter": "search::SelectPreviousMatch",
      "alt-enter": "search::SelectAllMatches",
      "find": "search::FocusSearch",
      "ctrl-f": "search::FocusSearch",
      "ctrl-h": "search::ToggleReplace",
      "ctrl-l": "search::ToggleSelection"
    }
  },
  {
    "context": "BufferSearchBar && in_replace > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "search::ReplaceNext",
      "ctrl-enter": "search::ReplaceAll"
    }
  },
  {
    "context": "BufferSearchBar && !in_replace > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "up": "search::PreviousHistoryQuery",
      "down": "search::NextHistoryQuery"
    }
  },
  {
    "context": "ProjectSearchBar",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "project_search::ToggleFocus",
      "shift-find": "search::FocusSearch",
      "ctrl-shift-f": "search::FocusSearch",
      "ctrl-shift-h": "search::ToggleReplace",
      "alt-r": "search::ToggleRegex" // vscode
    }
  },
  {
    "context": "ProjectSearchBar > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "up": "search::PreviousHistoryQuery",
      "down": "search::NextHistoryQuery"
    }
  },
  {
    "context": "ProjectSearchBar && in_replace > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "search::ReplaceNext",
      "ctrl-alt-enter": "search::ReplaceAll"
    }
  },
  {
    "context": "ProjectSearchView",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "project_search::ToggleFocus",
      "ctrl-shift-h": "search::ToggleReplace",
      "alt-r": "search::ToggleRegex" // vscode
    }
  },
  {
    "context": "Pane",
    "use_key_equivalents": true,
    "bindings": {
      "alt-1": ["pane::ActivateItem", 0],
      "alt-2": ["pane::ActivateItem", 1],
      "alt-3": ["pane::ActivateItem", 2],
      "alt-4": ["pane::ActivateItem", 3],
      "alt-5": ["pane::ActivateItem", 4],
      "alt-6": ["pane::ActivateItem", 5],
      "alt-7": ["pane::ActivateItem", 6],
      "alt-8": ["pane::ActivateItem", 7],
      "alt-9": ["pane::ActivateItem", 8],
      "alt-0": "pane::ActivateLastItem",
      "ctrl-pageup": "pane::ActivatePreviousItem",
      "ctrl-pagedown": "pane::ActivateNextItem",
      "ctrl-shift-pageup": "pane::SwapItemLeft",
      "ctrl-shift-pagedown": "pane::SwapItemRight",
      "ctrl-f4": ["pane::CloseActiveItem", { "close_pinned": false }],
      "ctrl-w": ["pane::CloseActiveItem", { "close_pinned": false }],
      "ctrl-shift-alt-t": ["pane::CloseOtherItems", { "close_pinned": false }],
      "ctrl-shift-alt-w": "workspace::CloseInactiveTabsAndPanes",
      "ctrl-k e": ["pane::CloseItemsToTheLeft", { "close_pinned": false }],
      "ctrl-k t": ["pane::CloseItemsToTheRight", { "close_pinned": false }],
      "ctrl-k u": ["pane::CloseCleanItems", { "close_pinned": false }],
      "ctrl-k w": ["pane::CloseAllItems", { "close_pinned": false }],
      "ctrl-k ctrl-w": "workspace::CloseAllItemsAndPanes",
      "back": "pane::GoBack",
      "alt--": "pane::GoBack",
      "alt-=": "pane::GoForward",
      "forward": "pane::GoForward",
      "f3": "search::SelectNextMatch",
      "shift-f3": "search::SelectPreviousMatch",
      "shift-find": "project_search::ToggleFocus",
      "ctrl-shift-f": "project_search::ToggleFocus",
      "shift-alt-h": "search::ToggleReplace",
      "alt-l": "search::ToggleSelection",
      "alt-enter": "search::SelectAllMatches",
      "alt-c": "search::ToggleCaseSensitive",
      "alt-w": "search::ToggleWholeWord",
      "alt-find": "project_search::ToggleFilters",
      "alt-f": "project_search::ToggleFilters",
      "alt-r": "search::ToggleRegex",
      // "ctrl-shift-alt-x": "search::ToggleRegex",
      "ctrl-k shift-enter": "pane::TogglePinTab"
    }
  },
  // Bindings from VS Code
  {
    "context": "Editor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-[": "editor::Outdent",
      "ctrl-]": "editor::Indent",
      "ctrl-shift-alt-up": "editor::AddSelectionAbove", // Insert Cursor Above
      "ctrl-shift-alt-down": "editor::AddSelectionBelow", // Insert Cursor Below
      "ctrl-shift-k": "editor::DeleteLine",
      "alt-up": "editor::MoveLineUp",
      "alt-down": "editor::MoveLineDown",
      "shift-alt-up": "editor::DuplicateLineUp",
      "shift-alt-down": "editor::DuplicateLineDown",
      "shift-alt-right": "editor::SelectLargerSyntaxNode", // Expand Selection
      "shift-alt-left": "editor::SelectSmallerSyntaxNode", // Shrink Selection
      "ctrl-shift-l": "editor::SelectAllMatches", // Select all occurrences of current selection
      "ctrl-f2": "editor::SelectAllMatches", // Select all occurrences of current word
      "ctrl-d": ["editor::SelectNext", { "replace_newest": false }], // editor.action.addSelectionToNextFindMatch  / find_under_expand
      "ctrl-shift-down": ["editor::SelectNext", { "replace_newest": false }], // editor.action.addSelectionToNextFindMatch
      "ctrl-shift-up": ["editor::SelectPrevious", { "replace_newest": false }], // editor.action.addSelectionToPreviousFindMatch
      "ctrl-k ctrl-d": ["editor::SelectNext", { "replace_newest": true }], // editor.action.moveSelectionToNextFindMatch  / find_under_expand_skip
      "ctrl-k ctrl-shift-d": ["editor::SelectPrevious", { "replace_newest": true }], // editor.action.moveSelectionToPreviousFindMatch
      "ctrl-k ctrl-i": "editor::Hover",
      "ctrl-k ctrl-b": "editor::BlameHover",
      "ctrl-/": ["editor::ToggleComments", { "advance_downwards": false }],
      "f8": ["editor::GoToDiagnostic", { "severity": { "min": "hint", "max": "error" } }],
      "shift-f8": ["editor::GoToPreviousDiagnostic", { "severity": { "min": "hint", "max": "error" } }],
      "f2": "editor::Rename",
      "f12": "editor::GoToDefinition",
      "alt-f12": "editor::GoToDefinitionSplit",
      "ctrl-shift-f10": "editor::GoToDefinitionSplit",
      "ctrl-f12": "editor::GoToImplementation",
      "shift-f12": "editor::GoToTypeDefinition",
      "ctrl-alt-f12": "editor::GoToTypeDefinitionSplit",
      "shift-alt-f12": "editor::FindAllReferences",
      "ctrl-m": "editor::MoveToEnclosingBracket", // from jetbrains
      "ctrl-shift-\\": "editor::MoveToEnclosingBracket",
      "ctrl-shift-[": "editor::Fold",
      "ctrl-shift-]": "editor::UnfoldLines",
      "ctrl-k ctrl-l": "editor::ToggleFold",
      "ctrl-k ctrl-[": "editor::FoldRecursive",
      "ctrl-k ctrl-]": "editor::UnfoldRecursive",
      "ctrl-k ctrl-1": ["editor::FoldAtLevel", 1],
      "ctrl-k ctrl-2": ["editor::FoldAtLevel", 2],
      "ctrl-k ctrl-3": ["editor::FoldAtLevel", 3],
      "ctrl-k ctrl-4": ["editor::FoldAtLevel", 4],
      "ctrl-k ctrl-5": ["editor::FoldAtLevel", 5],
      "ctrl-k ctrl-6": ["editor::FoldAtLevel", 6],
      "ctrl-k ctrl-7": ["editor::FoldAtLevel", 7],
      "ctrl-k ctrl-8": ["editor::FoldAtLevel", 8],
      "ctrl-k ctrl-9": ["editor::FoldAtLevel", 9],
      "ctrl-k ctrl-0": "editor::FoldAll",
      "ctrl-k ctrl-j": "editor::UnfoldAll",
      "ctrl-space": "editor::ShowCompletions",
      "ctrl-shift-space": "editor::ShowWordCompletions",
      "ctrl-.": "editor::ToggleCodeActions",
      "ctrl-k r": "editor::RevealInFileManager",
      "ctrl-k p": "editor::CopyPath",
      "ctrl-\\": "pane::SplitRight",
      "ctrl-shift-alt-c": "editor::DisplayCursorNames",
      "alt-.": "editor::GoToHunk",
      "alt-,": "editor::GoToPreviousHunk"
    }
  },
  {
    "context": "Editor && extension == md",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-k v": "markdown::OpenPreviewToTheSide",
      "ctrl-shift-v": "markdown::OpenPreview"
    }
  },
  {
    "context": "Editor && extension == svg",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-k v": "svg::OpenPreviewToTheSide",
      "ctrl-shift-v": "svg::OpenPreview"
    }
  },
  {
    "context": "Editor && mode == full",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-o": "outline::Toggle",
      "ctrl-g": "go_to_line::Toggle"
    }
  },
  {
    "context": "Workspace",
    "use_key_equivalents": true,
    "bindings": {
      "alt-open": ["projects::OpenRecent", { "create_new_window": false }],
      // Change the default action on `menu::Confirm` by setting the parameter
      // "ctrl-alt-o": ["projects::OpenRecent", { "create_new_window": true }],
      "ctrl-r": ["projects::OpenRecent", { "create_new_window": false }],
      "shift-alt-open": ["projects::OpenRemote", { "from_existing_connection": false, "create_new_window": false }],
      // Change to open path modal for existing remote connection by setting the parameter
      // "ctrl-shift-alt-o": "["projects::OpenRemote", { "from_existing_connection": true }]",
      "ctrl-shift-alt-o": ["projects::OpenRemote", { "from_existing_connection": false, "create_new_window": false }],
      "shift-alt-b": "branches::OpenRecent",
      "shift-alt-enter": "toast::RunAction",
      "ctrl-shift-`": "workspace::NewTerminal",
      "save": "workspace::Save",
      "ctrl-s": "workspace::Save",
      "ctrl-k ctrl-shift-s": "workspace::SaveWithoutFormat",
      "shift-save": "workspace::SaveAs",
      "ctrl-shift-s": "workspace::SaveAs",
      "new": "workspace::NewFile",
      "ctrl-n": "workspace::NewFile",
      "shift-new": "workspace::NewWindow",
      "ctrl-shift-n": "workspace::NewWindow",
      "ctrl-`": "terminal_panel::ToggleFocus",
      "f10": ["app_menu::OpenApplicationMenu", "Zed"],
      "alt-1": ["workspace::ActivatePane", 0],
      "alt-2": ["workspace::ActivatePane", 1],
      "alt-3": ["workspace::ActivatePane", 2],
      "alt-4": ["workspace::ActivatePane", 3],
      "alt-5": ["workspace::ActivatePane", 4],
      "alt-6": ["workspace::ActivatePane", 5],
      "alt-7": ["workspace::ActivatePane", 6],
      "alt-8": ["workspace::ActivatePane", 7],
      "alt-9": ["workspace::ActivatePane", 8],
      "ctrl-alt-b": "workspace::ToggleRightDock",
      "ctrl-b": "workspace::ToggleLeftDock",
      "ctrl-j": "workspace::ToggleBottomDock",
      "ctrl-shift-y": "workspace::CloseAllDocks",
      "alt-r": "workspace::ResetActiveDockSize",
      // For 0px parameter, uses UI font size value.
      "shift-alt--": ["workspace::DecreaseActiveDockSize", { "px": 0 }],
      "shift-alt-=": ["workspace::IncreaseActiveDockSize", { "px": 0 }],
      "shift-alt-0": "workspace::ResetOpenDocksSize",
      "ctrl-shift-alt--": ["workspace::DecreaseOpenDocksSize", { "px": 0 }],
      "ctrl-shift-alt-=": ["workspace::IncreaseOpenDocksSize", { "px": 0 }],
      "shift-find": "pane::DeploySearch",
      "ctrl-shift-f": "pane::DeploySearch",
      "ctrl-shift-h": ["pane::DeploySearch", { "replace_enabled": true }],
      "ctrl-shift-t": "pane::ReopenClosedItem",
      "ctrl-k ctrl-s": "zed::OpenKeymapEditor",
      "ctrl-k ctrl-t": "theme_selector::Toggle",
      "ctrl-alt-super-p": "settings_profile_selector::Toggle",
      "ctrl-t": "project_symbols::Toggle",
      "ctrl-p": "file_finder::Toggle",
      "ctrl-tab": "tab_switcher::Toggle",
      "ctrl-shift-tab": ["tab_switcher::Toggle", { "select_last": true }],
      "ctrl-e": "file_finder::Toggle",
      "f1": "command_palette::Toggle",
      "ctrl-shift-p": "command_palette::Toggle",
      "ctrl-shift-m": "diagnostics::Deploy",
      "ctrl-shift-e": "project_panel::ToggleFocus",
      "ctrl-shift-b": "outline_panel::ToggleFocus",
      "ctrl-shift-g": "git_panel::ToggleFocus",
      "ctrl-shift-d": "debug_panel::ToggleFocus",
      "ctrl-shift-/": "agent::ToggleFocus",
      "alt-save": "workspace::SaveAll",
      "ctrl-k s": "workspace::SaveAll",
      "ctrl-k m": "language_selector::Toggle",
      "escape": "workspace::Unfollow",
      "ctrl-k ctrl-left": "workspace::ActivatePaneLeft",
      "ctrl-k ctrl-right": "workspace::ActivatePaneRight",
      "ctrl-k ctrl-up": "workspace::ActivatePaneUp",
      "ctrl-k ctrl-down": "workspace::ActivatePaneDown",
      "ctrl-k shift-left": "workspace::SwapPaneLeft",
      "ctrl-k shift-right": "workspace::SwapPaneRight",
      "ctrl-k shift-up": "workspace::SwapPaneUp",
      "ctrl-k shift-down": "workspace::SwapPaneDown",
      "ctrl-shift-x": "zed::Extensions",
      "ctrl-shift-r": "task::Rerun",
      "alt-t": "task::Rerun",
      "shift-alt-t": "task::Spawn",
      "shift-alt-r": ["task::Spawn", { "reveal_target": "center" }],
      // also possible to spawn tasks by name:
      // "foo-bar": ["task::Spawn", { "task_name": "MyTask", "reveal_target": "dock" }]
      // or by tag:
      // "foo-bar": ["task::Spawn", { "task_tag": "MyTag" }],
      "f5": "debugger::Rerun",
      "ctrl-f4": "workspace::CloseActiveDock",
      "ctrl-w": "workspace::CloseActiveDock"
    }
  },
  {
    "context": "Workspace && debugger_running",
    "use_key_equivalents": true,
    "bindings": {
      "f5": "zed::NoAction"
    }
  },
  {
    "context": "Workspace && debugger_stopped",
    "use_key_equivalents": true,
    "bindings": {
      "f5": "debugger::Continue"
    }
  },
  {
    "context": "ApplicationMenu",
    "use_key_equivalents": true,
    "bindings": {
      "f10": "menu::Cancel",
      "left": "app_menu::ActivateMenuLeft",
      "right": "app_menu::ActivateMenuRight"
    }
  },
  // Bindings from Sublime Text
  {
    "context": "Editor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-u": "editor::UndoSelection",
      "ctrl-shift-u": "editor::RedoSelection",
      "ctrl-shift-j": "editor::JoinLines",
      "ctrl-alt-backspace": "editor::DeleteToPreviousSubwordStart",
      "shift-alt-h": "editor::DeleteToPreviousSubwordStart",
      "ctrl-alt-delete": "editor::DeleteToNextSubwordEnd",
      "shift-alt-d": "editor::DeleteToNextSubwordEnd",
      "ctrl-alt-left": "editor::MoveToPreviousSubwordStart",
      "ctrl-alt-right": "editor::MoveToNextSubwordEnd",
      "ctrl-shift-alt-left": "editor::SelectToPreviousSubwordStart",
      "ctrl-shift-alt-right": "editor::SelectToNextSubwordEnd"
    }
  },
  // Bindings from Atom
  {
    "context": "Pane",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-k up": "pane::SplitUp",
      "ctrl-k down": "pane::SplitDown",
      "ctrl-k left": "pane::SplitLeft",
      "ctrl-k right": "pane::SplitRight"
    }
  },
  // Bindings that should be unified with bindings for more general actions
  {
    "context": "Editor && renaming",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "editor::ConfirmRename"
    }
  },
  {
    "context": "Editor && showing_completions",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "editor::ConfirmCompletion",
      "shift-enter": "editor::ConfirmCompletionReplace",
      "tab": "editor::ComposeCompletion"
    }
  },
  // Bindings for accepting edit predictions
  //
  // alt-l is provided as an alternative to tab/alt-tab. and will be displayed in the UI. This is
  // because alt-tab may not be available, as it is often used for window switching.
  {
    "context": "Editor && edit_prediction",
    "use_key_equivalents": true,
    "bindings": {
      "alt-tab": "editor::AcceptEditPrediction",
      "alt-l": "editor::AcceptEditPrediction",
      "tab": "editor::AcceptEditPrediction",
      "alt-right": "editor::AcceptPartialEditPrediction"
    }
  },
  {
    "context": "Editor && edit_prediction_conflict",
    "use_key_equivalents": true,
    "bindings": {
      "alt-tab": "editor::AcceptEditPrediction",
      "alt-l": "editor::AcceptEditPrediction",
      "alt-right": "editor::AcceptPartialEditPrediction"
    }
  },
  {
    "context": "Editor && showing_code_actions",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "editor::ConfirmCodeAction"
    }
  },
  {
    "context": "Editor && (showing_code_actions || showing_completions)",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-p": "editor::ContextMenuPrevious",
      "up": "editor::ContextMenuPrevious",
      "ctrl-n": "editor::ContextMenuNext",
      "down": "editor::ContextMenuNext",
      "pageup": "editor::ContextMenuFirst",
      "pagedown": "editor::ContextMenuLast"
    }
  },
  {
    "context": "Editor && showing_signature_help && !showing_completions",
    "use_key_equivalents": true,
    "bindings": {
      "up": "editor::SignatureHelpPrevious",
      "down": "editor::SignatureHelpNext"
    }
  },
  // Custom bindings
  {
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-alt-f": "workspace::FollowNextCollaborator",
      // Only available in debug builds: opens an element inspector for development.
      "shift-alt-i": "dev::ToggleInspector"
    }
  },
  {
    "context": "!Terminal",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-c": "collab_panel::ToggleFocus"
    }
  },
  {
    "context": "!ContextEditor > Editor && mode == full",
    "use_key_equivalents": true,
    "bindings": {
      "alt-enter": "editor::OpenExcerpts",
      "shift-enter": "editor::ExpandExcerpts",
      "ctrl-alt-enter": "editor::OpenExcerptsSplit",
      "ctrl-shift-e": "pane::RevealInProjectPanel",
      "ctrl-f8": "editor::GoToHunk",
      "ctrl-shift-f8": "editor::GoToPreviousHunk",
      "ctrl-enter": "assistant::InlineAssist",
      "ctrl-shift-;": "editor::ToggleInlayHints"
    }
  },
  {
    "context": "PromptEditor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-[": "agent::CyclePreviousInlineAssist",
      "ctrl-]": "agent::CycleNextInlineAssist",
      "shift-alt-e": "agent::RemoveAllContext"
    }
  },
  {
    "context": "Prompt",
    "use_key_equivalents": true,
    "bindings": {
      "left": "menu::SelectPrevious",
      "right": "menu::SelectNext",
      "h": "menu::SelectPrevious",
      "l": "menu::SelectNext"
    }
  },
  {
    "context": "ProjectSearchBar && !in_replace",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "project_search::SearchInNew"
    }
  },
  {
    "context": "OutlinePanel && not_editing",
    "use_key_equivalents": true,
    "bindings": {
      "left": "outline_panel::CollapseSelectedEntry",
      "right": "outline_panel::ExpandSelectedEntry",
      "alt-copy": "outline_panel::CopyPath",
      "shift-alt-c": "outline_panel::CopyPath",
      "shift-alt-copy": "workspace::CopyRelativePath",
      "ctrl-shift-alt-c": "workspace::CopyRelativePath",
      "ctrl-alt-r": "outline_panel::RevealInFileManager",
      "space": "outline_panel::OpenSelectedEntry",
      "shift-down": "menu::SelectNext",
      "shift-up": "menu::SelectPrevious",
      "alt-enter": "editor::OpenExcerpts",
      "ctrl-alt-enter": "editor::OpenExcerptsSplit"
    }
  },
  {
    "context": "ProjectPanel",
    "use_key_equivalents": true,
    "bindings": {
      "left": "project_panel::CollapseSelectedEntry",
      "right": "project_panel::ExpandSelectedEntry",
      "new": "project_panel::NewFile",
      "ctrl-n": "project_panel::NewFile",
      "alt-new": "project_panel::NewDirectory",
      "alt-n": "project_panel::NewDirectory",
      "cut": "project_panel::Cut",
      "ctrl-x": "project_panel::Cut",
      "copy": "project_panel::Copy",
      "ctrl-insert": "project_panel::Copy",
      "ctrl-c": "project_panel::Copy",
      "paste": "project_panel::Paste",
      "shift-insert": "project_panel::Paste",
      "ctrl-v": "project_panel::Paste",
      "alt-copy": "project_panel::CopyPath",
      "shift-alt-c": "project_panel::CopyPath",
      "shift-alt-copy": "workspace::CopyRelativePath",
      "ctrl-k ctrl-shift-c": "workspace::CopyRelativePath",
      "enter": "project_panel::Rename",
      "f2": "project_panel::Rename",
      "backspace": ["project_panel::Trash", { "skip_prompt": false }],
      "delete": ["project_panel::Trash", { "skip_prompt": false }],
      "shift-delete": ["project_panel::Delete", { "skip_prompt": false }],
      "ctrl-backspace": ["project_panel::Delete", { "skip_prompt": false }],
      "ctrl-delete": ["project_panel::Delete", { "skip_prompt": false }],
      "ctrl-alt-r": "project_panel::RevealInFileManager",
      "ctrl-shift-enter": "project_panel::OpenWithSystem",
      "alt-d": "project_panel::CompareMarkedFiles",
      "shift-find": "project_panel::NewSearchInDirectory",
      "ctrl-k ctrl-shift-f": "project_panel::NewSearchInDirectory",
      "shift-down": "menu::SelectNext",
      "shift-up": "menu::SelectPrevious",
      "escape": "menu::Cancel"
    }
  },
  {
    "context": "ProjectPanel && not_editing",
    "use_key_equivalents": true,
    "bindings": {
      "space": "project_panel::Open"
    }
  },
  {
    "context": "GitPanel && ChangesList",
    "use_key_equivalents": true,
    "bindings": {
      "up": "menu::SelectPrevious",
      "down": "menu::SelectNext",
      "enter": "menu::Confirm",
      "alt-y": "git::StageFile",
      "shift-alt-y": "git::UnstageFile",
      "space": "git::ToggleStaged",
      "shift-space": "git::StageRange",
      "tab": "git_panel::FocusEditor",
      "shift-tab": "git_panel::FocusEditor",
      "escape": "git_panel::ToggleFocus",
      "alt-enter": "menu::SecondaryConfirm",
      "delete": ["git::RestoreFile", { "skip_prompt": false }],
      "backspace": ["git::RestoreFile", { "skip_prompt": false }],
      "shift-delete": ["git::RestoreFile", { "skip_prompt": false }],
      "ctrl-backspace": ["git::RestoreFile", { "skip_prompt": false }],
      "ctrl-delete": ["git::RestoreFile", { "skip_prompt": false }]
    }
  },
  {
    "context": "GitPanel && CommitEditor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "git::Cancel"
    }
  },
  {
    "context": "GitCommit > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel",
      "enter": "editor::Newline",
      "ctrl-enter": "git::Commit",
      "ctrl-shift-enter": "git::Amend",
      "alt-l": "git::GenerateCommitMessage"
    }
  },
  {
    "context": "GitPanel",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-g ctrl-g": "git::Fetch",
      "ctrl-g up": "git::Push",
      "ctrl-g down": "git::Pull",
      "ctrl-g shift-up": "git::ForcePush",
      "ctrl-g d": "git::Diff",
      "ctrl-g backspace": "git::RestoreTrackedFiles",
      "ctrl-g shift-backspace": "git::TrashUntrackedFiles",
      "ctrl-space": "git::StageAll",
      "ctrl-shift-space": "git::UnstageAll",
      "ctrl-enter": "git::Commit",
      "ctrl-shift-enter": "git::Amend"
    }
  },
  {
    "context": "GitDiff > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "git::Commit",
      "ctrl-shift-enter": "git::Amend",
      "ctrl-space": "git::StageAll",
      "ctrl-shift-space": "git::UnstageAll"
    }
  },
  {
    "context": "AskPass > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "menu::Confirm"
    }
  },
  {
    "context": "CommitEditor > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "git_panel::FocusChanges",
      "tab": "git_panel::FocusChanges",
      "shift-tab": "git_panel::FocusChanges",
      "enter": "editor::Newline",
      "ctrl-enter": "git::Commit",
      "ctrl-shift-enter": "git::Amend",
      "alt-up": "git_panel::FocusChanges",
      "alt-l": "git::GenerateCommitMessage"
    }
  },
  {
    "context": "DebugPanel",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-t": "debugger::ToggleThreadPicker",
      "ctrl-i": "debugger::ToggleSessionPicker",
      "shift-alt-escape": "debugger::ToggleExpandItem"
    }
  },
  {
    "context": "VariableList",
    "use_key_equivalents": true,
    "bindings": {
      "left": "variable_list::CollapseSelectedEntry",
      "right": "variable_list::ExpandSelectedEntry",
      "enter": "variable_list::EditVariable",
      "ctrl-c": "variable_list::CopyVariableValue",
      "ctrl-alt-c": "variable_list::CopyVariableName",
      "delete": "variable_list::RemoveWatch",
      "backspace": "variable_list::RemoveWatch",
      "alt-enter": "variable_list::AddWatch"
    }
  },
  {
    "context": "BreakpointList",
    "use_key_equivalents": true,
    "bindings": {
      "space": "debugger::ToggleEnableBreakpoint",
      "backspace": "debugger::UnsetBreakpoint",
      "left": "debugger::PreviousBreakpointProperty",
      "right": "debugger::NextBreakpointProperty"
    }
  },
  {
    "context": "CollabPanel && not_editing",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-backspace": "collab_panel::Remove",
      "space": "menu::Confirm"
    }
  },
  {
    "context": "CollabPanel",
    "use_key_equivalents": true,
    "bindings": {
      "alt-up": "collab_panel::MoveChannelUp",
      "alt-down": "collab_panel::MoveChannelDown"
    }
  },
  {
    "context": "(CollabPanel && editing) > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "space": "collab_panel::InsertSpace"
    }
  },
  {
    "context": "ChannelModal",
    "use_key_equivalents": true,
    "bindings": {
      "tab": "channel_modal::ToggleMode"
    }
  },
  {
    "context": "Picker > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel",
      "up": "menu::SelectPrevious",
      "down": "menu::SelectNext",
      "tab": "picker::ConfirmCompletion",
      "alt-enter": ["picker::ConfirmInput", { "secondary": false }]
    }
  },
  {
    "context": "ChannelModal > Picker > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "tab": "channel_modal::ToggleMode"
    }
  },
  {
    "context": "FileFinder || (FileFinder > Picker > Editor)",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-p": "file_finder::Toggle",
      "ctrl-shift-a": "file_finder::ToggleSplitMenu",
      "ctrl-shift-i": "file_finder::ToggleFilterMenu"
    }
  },
  {
    "context": "FileFinder || (FileFinder > Picker > Editor) || (FileFinder > Picker > menu)",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-p": "file_finder::SelectPrevious",
      "ctrl-j": "pane::SplitDown",
      "ctrl-k": "pane::SplitUp",
      "ctrl-h": "pane::SplitLeft",
      "ctrl-l": "pane::SplitRight"
    }
  },
  {
    "context": "TabSwitcher",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-shift-tab": "menu::SelectPrevious",
      "ctrl-up": "menu::SelectPrevious",
      "ctrl-down": "menu::SelectNext",
      "ctrl-backspace": "tab_switcher::CloseSelectedItem"
    }
  },
  {
    "context": "Terminal",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-alt-space": "terminal::ShowCharacterPalette",
      "copy": "terminal::Copy",
      "ctrl-insert": "terminal::Copy",
      "ctrl-shift-c": "terminal::Copy",
      "paste": "terminal::Paste",
      "shift-insert": "terminal::Paste",
      "ctrl-shift-v": "terminal::Paste",
      "ctrl-enter": "assistant::InlineAssist",
      "alt-b": ["terminal::SendText", "\u001bb"],
      "alt-f": ["terminal::SendText", "\u001bf"],
      "alt-.": ["terminal::SendText", "\u001b."],
      "ctrl-delete": ["terminal::SendText", "\u001bd"],
      // Overrides for conflicting keybindings
      "ctrl-b": ["terminal::SendKeystroke", "ctrl-b"],
      "ctrl-c": ["terminal::SendKeystroke", "ctrl-c"],
      "ctrl-e": ["terminal::SendKeystroke", "ctrl-e"],
      "ctrl-o": ["terminal::SendKeystroke", "ctrl-o"],
      "ctrl-w": ["terminal::SendKeystroke", "ctrl-w"],
      "ctrl-backspace": ["terminal::SendKeystroke", "ctrl-w"],
      "ctrl-shift-a": "editor::SelectAll",
      "find": "buffer_search::Deploy",
      "ctrl-shift-f": "buffer_search::Deploy",
      "ctrl-shift-l": "terminal::Clear",
      "ctrl-shift-w": "pane::CloseActiveItem",
      "up": ["terminal::SendKeystroke", "up"],
      "pageup": ["terminal::SendKeystroke", "pageup"],
      "down": ["terminal::SendKeystroke", "down"],
      "pagedown": ["terminal::SendKeystroke", "pagedown"],
      "escape": ["terminal::SendKeystroke", "escape"],
      "enter": ["terminal::SendKeystroke", "enter"],
      "shift-pageup": "terminal::ScrollPageUp",
      "shift-pagedown": "terminal::ScrollPageDown",
      "shift-up": "terminal::ScrollLineUp",
      "shift-down": "terminal::ScrollLineDown",
      "shift-home": "terminal::ScrollToTop",
      "shift-end": "terminal::ScrollToBottom",
      "ctrl-shift-space": "terminal::ToggleViMode",
      "ctrl-shift-r": "terminal::RerunTask",
      "ctrl-alt-r": "terminal::RerunTask",
      "alt-t": "terminal::RerunTask"
    }
  },
  {
    "context": "ZedPredictModal",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel"
    }
  },
  {
    "context": "ConfigureContextServerModal > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel",
      "enter": "editor::Newline",
      "ctrl-enter": "menu::Confirm"
    }
  },
  {
    "context": "OnboardingAiConfigurationModal",
    "use_key_equivalents": true,
    "bindings": {
      "escape": "menu::Cancel"
    }
  },
  {
    "context": "Diagnostics",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-r": "diagnostics::ToggleDiagnosticsRefresh"
    }
  },
  {
    "context": "DebugConsole > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "menu::Confirm",
      "alt-enter": "console::WatchExpression"
    }
  },
  {
    "context": "RunModal",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-tab": "pane::ActivateNextItem",
      "ctrl-shift-tab": "pane::ActivatePreviousItem"
    }
  },
  {
    "context": "MarkdownPreview",
    "use_key_equivalents": true,
    "bindings": {
      "pageup": "markdown::MovePageUp",
      "pagedown": "markdown::MovePageDown"
    }
  },
  {
    "context": "KeymapEditor",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-f": "search::FocusSearch",
      "alt-find": "keymap_editor::ToggleKeystrokeSearch",
      "alt-f": "keymap_editor::ToggleKeystrokeSearch",
      "alt-c": "keymap_editor::ToggleConflictFilter",
      "enter": "keymap_editor::EditBinding",
      "alt-enter": "keymap_editor::CreateBinding",
      "ctrl-c": "keymap_editor::CopyAction",
      "ctrl-shift-c": "keymap_editor::CopyContext",
      "ctrl-t": "keymap_editor::ShowMatchingKeybinds"
    }
  },
  {
    "context": "KeystrokeInput",
    "use_key_equivalents": true,
    "bindings": {
      "enter": "keystroke_input::StartRecording",
      "escape escape escape": "keystroke_input::StopRecording",
      "delete": "keystroke_input::ClearKeystrokes"
    }
  },
  {
    "context": "KeybindEditorModal",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-enter": "menu::Confirm",
      "escape": "menu::Cancel"
    }
  },
  {
    "context": "KeybindEditorModal > Editor",
    "use_key_equivalents": true,
    "bindings": {
      "up": "menu::SelectPrevious",
      "down": "menu::SelectNext"
    }
  },
  {
    "context": "Onboarding",
    "use_key_equivalents": true,
    "bindings": {
      "ctrl-1": "onboarding::ActivateBasicsPage",
      "ctrl-2": "onboarding::ActivateEditingPage",
      "ctrl-3": "onboarding::ActivateAISetupPage",
      "ctrl-escape": "onboarding::Finish",
      "alt-tab": "onboarding::SignIn",
      "shift-alt-a": "onboarding::OpenAccount"
    }
  }
]
