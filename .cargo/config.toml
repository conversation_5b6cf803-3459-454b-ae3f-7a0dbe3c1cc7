[build]
# v0 mangling scheme provides more detailed backtraces around closures
rustflags = ["-C", "symbol-mangling-version=v0", "--cfg", "tokio_unstable"]

[alias]
xtask = "run --package xtask --"

[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=mold"]

[target.aarch64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=mold"]

[target.'cfg(target_os = "windows")']
rustflags = [
    "--cfg",
    "windows_slim_errors",        # This cfg will reduce the size of `windows::core::Error` from 16 bytes to 4 bytes
    "-C",
    "target-feature=+crt-static", # This fixes the linking issue when compiling livekit on Windows
    "-C",
    "link-arg=-fuse-ld=lld",
]

[env]
MACOSX_DEPLOYMENT_TARGET = "10.15.7"
